// Station Details Page - Shows detailed information about a charging station
// This page is divided into three main sections:
// 1. Station Information - Header, station details, etc.
// 2. Connector Selection - Connector cards, connector groups
// 3. Bottom Bar and Actions - Charge button, connector selection

import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../models/station_converter.dart';
import '../../models/station.dart';
import '../../models/station/station_details_response.dart' as station_details;
import '../../services/api_bridge.dart';
import '../../services/token_service.dart';
import '../../widgets/station_loading_animation.dart';

class StationDetailsPage extends StatefulWidget {
  final String uid; // UID is required for API calls
  final Station? station; // Optional station object

  const StationDetailsPage({
    super.key,
    required this.uid,
    this.station,
  });

  @override
  StationDetailsPageState createState() => StationDetailsPageState();
}

class StationDetailsPageState extends State<StationDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // API service
  final ApiBridge _apiBridge = ApiBridge();

  // State variables
  bool _isLoading = true;
  bool _isLoadingReviews = false;
  String _errorMessage = '';
  Station? _station;
  List<dynamic> _reviews = [];

  // Local cache for connectors to avoid repeated API calls
  static final Map<String, List<station_details.Connector>> _connectorsCache =
      {};

  @override
  void initState() {
    super.initState();

    // Initialize tab controller
    _tabController = TabController(length: 2, vsync: this);

    // If we have a station from the constructor, use it
    if (widget.station != null) {
      setState(() {
        _station = widget.station;
        _isLoading = false;
      });
    } else {
      // Otherwise fetch station details from API
      _fetchStationDetails();
    }

    // Fetch reviews
    _fetchReviews();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Fetch station details using the UID with enhanced error handling and retry logic
  Future<void> _fetchStationDetails() async {
    if (!mounted) return;

    // Reset state to trigger loading animation
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    final String uidToUse = widget.uid.trim();

    // Validate UID
    if (uidToUse.isEmpty) {
      setState(() {
        _errorMessage = 'Station ID is missing. Please try again.';
        _isLoading = false;
      });
      return;
    }

    // Reset station data
    setState(() {
      _station = null;
    });

    // Maximum number of retry attempts
    const int maxRetries = 3;
    int attempt = 0;
    bool success = false;

    while (attempt < maxRetries && !success) {
      attempt++;

      try {
        // Get station details from API with timeout
        final stationDetails = await _apiBridge
            .getStationDetailsByUid(uidToUse)
            .timeout(const Duration(seconds: 15));

        if (stationDetails == null || stationDetails.data == null) {
          throw Exception('Invalid response from server');
        }

        // Log all station details for debugging
        debugPrint('Successfully fetched station details:');
        debugPrint('Station name: ${stationDetails.data!.name}');
        debugPrint('Station address: ${stationDetails.data!.address}');
        debugPrint('Station city: ${stationDetails.data!.city}');
        debugPrint('Station state: ${stationDetails.data!.state}');
        debugPrint('Station rating: ${stationDetails.data!.rate}');
        debugPrint('Station reviews: ${stationDetails.data!.rateTotal}');
        debugPrint(
            'Station opening times: ${stationDetails.data!.openingTimes}');
        debugPrint('Station open status: ${stationDetails.data!.openStatus}');
        debugPrint('Station UID: ${stationDetails.data!.uid}');

        // Validate required fields before proceeding
        if (stationDetails.data!.latitude == null) {
          throw Exception('No latitude found in station details');
        }
        if (stationDetails.data!.longitude == null) {
          throw Exception('No longitude found in station details');
        }
        if (stationDetails.data!.evses == null ||
            stationDetails.data!.evses!.isEmpty) {
          throw Exception('No EVSEs found in station details response');
        }
        if (stationDetails.data!.name == null ||
            stationDetails.data!.name!.isEmpty) {
          throw Exception('No station name found in response');
        }
        if (stationDetails.data!.address == null ||
            stationDetails.data!.address!.isEmpty) {
          throw Exception('No station address found in response');
        }

        // Process the station data using the StationConverter
        final station =
            StationConverter.fromStationDetail(stationDetails.data!);

        // Extract connectors from the EVSEs if available
        List<station_details.Connector> updatedConnectors = [];
        if (stationDetails.data!.evses != null) {
          stationDetails.data!.evses!.forEach((evsesUid, evse) {
            debugPrint('Processing EVSE with UID: $evsesUid');
            if (evse.connectors != null) {
              for (var connector in evse.connectors!) {
                try {
                  // Validate connector data
                  if (connector.type == null || connector.type!.isEmpty) {
                    debugPrint('Skipping connector with missing type');
                    continue;
                  }

                  // Log power and price values for debugging
                  debugPrint(
                      'Connector power: ${connector.maxElectricPower} kW');
                  debugPrint('Connector price: ${connector.pricePerUnit}/kWh');

                  // Always set the EVSE UID on the connector
                  connector.evsesUid = evsesUid;
                  debugPrint('Set EVSE UID on connector: $evsesUid');

                  // Add the connector to our list
                  updatedConnectors.add(connector);
                } catch (e) {
                  debugPrint('Error processing connector: $e');
                  // Continue with other connectors even if one fails
                }
              }
            }
          });
        }

        // Cache the connectors for this station
        if (updatedConnectors.isNotEmpty) {
          _connectorsCache[uidToUse] = updatedConnectors;

          // We have connectors available
          debugPrint('Found ${updatedConnectors.length} connectors');
        }

        // Update the UI with the station data
        if (mounted) {
          setState(() {
            _station = station;
            _isLoading = false;
            _errorMessage = '';
          });
        }

        success = true;
      } on TimeoutException {
        if (attempt >= maxRetries) {
          if (mounted) {
            setState(() {
              _errorMessage =
                  'Connection timed out. Please check your internet connection.';
              _isLoading = false;
            });
          }
        } else {
          // Wait before retrying (exponential backoff)
          await Future.delayed(Duration(seconds: 1 * attempt));
        }
      } on Exception {
        if (attempt >= maxRetries) {
          if (mounted) {
            setState(() {
              _errorMessage =
                  'Failed to load station details. Please try again later.';
              _isLoading = false;
            });
          }
        } else {
          await Future.delayed(Duration(seconds: 1 * attempt));
        }
      }
    }

    // If we have data but no connectors, show a warning
    if (mounted && _station != null && _station!.connectors.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No available connectors at this station'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  // Fetch reviews for the station
  Future<void> _fetchReviews() async {
    // The location_id for reviews is the same as the UID used to fetch station details
    final String locationId = widget.uid;

    if (locationId.isEmpty) {
      debugPrint('Cannot fetch reviews: Location ID (UID) is empty');
      setState(() {
        _isLoadingReviews = false;
      });
      return;
    }

    // Only set loading state if it's not already set
    if (!_isLoadingReviews) {
      setState(() {
        _isLoadingReviews = true;
      });
    }

    try {
      debugPrint('Fetching reviews for location ID: $locationId');

      // Make a direct HTTP request to the reviews API
      final url =
          'https://api2.eeil.online/api/v1/user/reviews?location_id=$locationId';

      // Get auth token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      debugPrint(
          'Auth token from SharedPreferences: ${token != null ? "Found" : "Not found"}');

      String? authToken = token;

      if (token == null || token.isEmpty) {
        debugPrint('No auth token available for reviews API request');
        // Try to get token from TokenService as fallback
        final tokenService = TokenService();
        final fallbackToken = await tokenService.getToken();

        if (fallbackToken == null || fallbackToken.isEmpty) {
          debugPrint('No auth token available from TokenService either');
          setState(() {
            _isLoadingReviews = false;
            _reviews = [];
          });
          return;
        }

        debugPrint('Using fallback token from TokenService');
        authToken = fallbackToken;
      }

      // Create headers with auth token
      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $authToken',
      };

      debugPrint('Making request to: $url');
      // Make the request
      final response = await http.get(Uri.parse(url), headers: headers);
      debugPrint('Response status code: ${response.statusCode}');

      if (mounted) {
        setState(() {
          if (response.statusCode == 200) {
            final jsonData = jsonDecode(response.body);
            debugPrint('Response body: ${response.body}');

            if (jsonData['success'] == true) {
              // Parse reviews from the response
              _reviews = (jsonData['data'] as List<dynamic>).map((review) {
                return review as Map<String, dynamic>;
              }).toList();

              debugPrint('Successfully fetched ${_reviews.length} reviews');
            } else {
              debugPrint(
                  'Failed to fetch reviews: ${jsonData['message'] ?? 'Unknown error'}');
              _reviews = [];
            }
          } else {
            debugPrint(
                'Error fetching reviews: ${response.statusCode} - ${response.body}');
            _reviews = [];
          }

          // Only update the reviews loading state, not the main loading state
          _isLoadingReviews = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingReviews = false;
          _reviews = [];
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show loading animation until ALL data is completely fetched from the API
    if (_isLoading || _station == null) {
      return Scaffold(
        // No app bar with back button in loading state
        body: StationLoadingAnimation(
          errorMessage: _errorMessage,
          onRetry: _fetchStationDetails,
        ),
      );
    }

    // Extract station data
    final stationName = _station!.name;
    final stationAddress = _station!.address;
    final stationRating = _station!.rating;
    final stationReviews = _station!.reviews;
    final stationStatus = _station!.status;
    final stationConnectors = _station!.connectors;

    // Use the station's opening times from the API
    final operatingHours = _station!.openingTimes ?? 'Unknown';

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // App bar with back button, station name, and refresh button
            AppBar(
              title: Text(stationName),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.of(context).pop(),
              ),
              actions: [
                // Refresh button to reload station data
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _fetchStationDetails,
                  tooltip: 'Refresh station data',
                ),
              ],
            ),

            // Main content with tabs
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Chargers tab
                  SingleChildScrollView(
                    child: Column(
                      children: [
                        // Station info card
                        Card(
                          margin: const EdgeInsets.all(16),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Station name
                                Text(
                                  stationName,
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),

                                // Station address
                                Text(
                                  stationAddress,
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),

                                // Station status
                                Text(
                                  'Status: $stationStatus',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),

                                // Station operating hours
                                Text(
                                  'Operating Hours: $operatingHours',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),

                                // Station rating
                                Row(
                                  children: [
                                    const Icon(Icons.star, color: Colors.amber),
                                    Text(
                                      '$stationRating ($stationReviews reviews)',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Connectors list
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: stationConnectors.length,
                          itemBuilder: (context, index) {
                            final connector = stationConnectors[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              child: ListTile(
                                leading: Icon(Icons.electrical_services),
                                title: Text(connector.name),
                                subtitle: Text(
                                    '${connector.power} • ${connector.status}'),
                                trailing: Text('${connector.price} PLN'),
                                onTap: () {
                                  // Handle connector selection
                                },
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  // Reviews tab
                  Center(
                    child: Text('Reviews tab content'),
                  ),
                ],
              ),
            ),

            // Bottom tab bar
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Chargers'),
                Tab(text: 'Reviews'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
