import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../providers/theme_provider.dart';
import '../utils/app_themes.dart';

class StationListSheet extends ConsumerStatefulWidget {
  final DraggableScrollableController sheetController;
  final TextEditingController searchController;
  final List<Map<String, dynamic>> stations;
  final List<Map<String, dynamic>> filteredStations;
  final bool isLoading;
  final bool isSearching;
  final bool isSearchLoading;
  final Function(BuildContext) showFilterOptions;
  final Function(Map<String, dynamic>) buildStationCard;
  final VoidCallback onSearchChanged;
  final LatLng? initialPosition;
  final Function(Map<String, dynamic>) onMarkerTapped;
  final VoidCallback onBackPressed;
  final double minSheetSize;
  final double midSheetSize;
  final double maxSheetSize;
  final bool showingNearestStations;
  // Removed onRefresh - refresh functionality only available through map controls
  final Function(Map<String, bool>, bool)? onFilter;
  // New error handling properties
  final String? errorMessage;
  final bool hasApiError;
  final VoidCallback? onRetry;
  final VoidCallback? onExpandSearchRadius;

  StationListSheet({
    super.key,
    required this.sheetController,
    required this.searchController,
    required this.stations,
    required this.filteredStations,
    required this.isLoading,
    required this.isSearching,
    this.isSearchLoading = false,
    required this.showFilterOptions,
    required this.buildStationCard,
    required this.onSearchChanged,
    this.initialPosition,
    required this.onMarkerTapped,
    required this.onBackPressed,
    this.minSheetSize = 0.28,
    this.midSheetSize = 0.4,
    this.maxSheetSize = 0.85,
    this.showingNearestStations = false,
    this.onFilter,
    // New error handling parameters
    this.errorMessage,
    this.hasApiError = false,
    this.onRetry,
    this.onExpandSearchRadius,
  }) {
    debugPrint(
        'StationListSheet constructor called with ${stations.length} stations');
    for (var station in stations) {
      debugPrint('Station: ${station['name']}');
    }
  }

  @override
  ConsumerState<StationListSheet> createState() => _StationListSheetState();
}

class _StationListSheetState extends ConsumerState<StationListSheet> {
  late ValueNotifier<double> _positionNotifier;

  // Loading duration management
  DateTime? _loadingStartTime;
  bool _hasMinimumLoadingElapsed = false;
  bool _apiCallCompleted = false;
  static const Duration _minimumLoadingDuration = Duration(milliseconds: 800);

  @override
  void initState() {
    super.initState();
    _positionNotifier = ValueNotifier<double>(widget.sheetController.isAttached
        ? widget.sheetController.size
        : widget.minSheetSize);

    if (widget.sheetController.isAttached) {
      widget.sheetController.addListener(_updatePositionNotifier);
    }

    // Initialize loading tracking
    _initializeLoadingTracking();
  }

  void _initializeLoadingTracking() {
    // Start tracking loading time when component initializes with loading state
    if (widget.isLoading && widget.stations.isEmpty) {
      _loadingStartTime = DateTime.now();
      _hasMinimumLoadingElapsed = false;
      _apiCallCompleted = false;

      // Set timer for minimum loading duration
      Future.delayed(_minimumLoadingDuration, () {
        if (mounted) {
          setState(() {
            _hasMinimumLoadingElapsed = true;
          });
        }
      });
    } else if (!widget.isLoading && widget.stations.isNotEmpty) {
      // If component initializes with data already loaded, allow immediate display
      _hasMinimumLoadingElapsed = true;
      _apiCallCompleted = true;
    } else if (!widget.isLoading && widget.stations.isEmpty) {
      // If component initializes with no loading and no data, this might be an error state
      // But don't show error immediately - wait for proper API response
      _hasMinimumLoadingElapsed = false;
      _apiCallCompleted = false;
    }
  }

  void _updatePositionNotifier() {
    if (widget.sheetController.isAttached) {
      _positionNotifier.value = widget.sheetController.size;
    }
  }

  @override
  void didUpdateWidget(StationListSheet oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Track when API call starts
    if (!oldWidget.isLoading && widget.isLoading && widget.stations.isEmpty) {
      _loadingStartTime = DateTime.now();
      _hasMinimumLoadingElapsed = false;
      _apiCallCompleted = false;

      // Set timer for minimum loading duration
      Future.delayed(_minimumLoadingDuration, () {
        if (mounted) {
          setState(() {
            _hasMinimumLoadingElapsed = true;
          });
        }
      });
    }

    // Track when API call completes (loading stops)
    if (oldWidget.isLoading && !widget.isLoading) {
      _apiCallCompleted = true;
    }
  }

  // Check if we should show final state (not loading)
  bool get _shouldShowFinalState {
    // If not loading, API has completed
    if (!widget.isLoading) {
      _apiCallCompleted = true;
    }

    // Always allow showing final state if API is not loading
    // Remove minimum duration dependency to prevent infinite loading
    final shouldShow = _apiCallCompleted || !widget.isLoading;

    debugPrint(
        'StationListSheet: _shouldShowFinalState = $shouldShow (apiCompleted: $_apiCallCompleted, isLoading: ${widget.isLoading}, minDurationElapsed: $_hasMinimumLoadingElapsed)');

    return shouldShow;
  }

  @override
  void dispose() {
    if (widget.sheetController.isAttached) {
      widget.sheetController.removeListener(_updatePositionNotifier);
    }
    _positionNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the keyboard height to adjust the sheet
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return DraggableScrollableSheet(
      controller: widget.sheetController,
      initialChildSize: widget
          .minSheetSize, // Start minimized, will be expanded programmatically
      minChildSize: widget.minSheetSize,
      maxChildSize: widget.maxSheetSize,
      snap: true,
      snapSizes: [
        widget.minSheetSize,
        widget.midSheetSize,
        widget.maxSheetSize
      ],
      // ENHANCED: More responsive physics and smoother gestures
      builder: (context, scrollController) {
        return NotificationListener<ScrollNotification>(
          onNotification: (notification) {
            // Handle all scroll notifications to implement physics-based behavior
            if (notification is ScrollEndNotification) {
              // When user stops scrolling, check if there's empty space at the bottom
              if (scrollController.hasClients) {
                final maxScroll = scrollController.position.maxScrollExtent;
                final currentScroll = scrollController.position.pixels;
                final viewportDimension =
                    scrollController.position.viewportDimension;

                // If we're not at the top and there's empty space (content doesn't fill viewport)
                if (currentScroll > 0 && maxScroll < viewportDimension) {
                  // Snap to the bottom with physics-based animation
                  scrollController.animateTo(
                    0, // Scroll to top since content is shorter than viewport
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOutCubic,
                  );
                } else if (currentScroll < maxScroll) {
                  // If we're not at the bottom and user released, snap to show the last card
                  final remainingScrollableDistance = maxScroll - currentScroll;

                  // If the remaining distance is less than 20% of viewport, snap to bottom
                  if (remainingScrollableDistance < viewportDimension * 0.2) {
                    scrollController.animateTo(
                      maxScroll,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOutCubic,
                    );
                  }
                }
              }
            }
            return false;
          },
          child: ValueListenableBuilder<double>(
            valueListenable: _positionNotifier,
            builder: (context, sheetSize, child) {
              // Calculate fill factor for potential future use
              // Uncomment when needed
              // final fillFactor = (sheetSize - widget.minSheetSize) /
              //     (widget.maxSheetSize - widget.minSheetSize);
              // final clampedFillFactor = fillFactor.clamp(0.0, 1.0);

              // Use theme-aware styling that adapts to light/dark mode
              final themeNotifier = ref.watch(themeNotifierProvider.notifier);
              final isDarkMode = themeNotifier.isDarkMode;
              final theme = Theme.of(context);

              return Theme(
                data: theme.copyWith(
                  // Use theme-aware colors instead of hardcoded values
                  cardTheme: CardThemeData(
                    color: isDarkMode ? AppThemes.darkCard : Colors.white,
                    elevation: isDarkMode ? 0 : 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: isDarkMode
                          ? const BorderSide(
                              color: AppThemes.darkBorder, width: 1)
                          : BorderSide.none,
                    ),
                  ),
                ),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOutCubic,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: isDarkMode ? AppThemes.darkSurface : Colors.white,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(20),
                      // Removed curved bottom - using flat bottom edge
                      bottom: Radius.zero,
                    ),
                    border: isDarkMode
                        ? Border.all(color: AppThemes.darkBorder, width: 1)
                        : null,
                    boxShadow: isDarkMode
                        ? [
                            BoxShadow(
                              color: Colors.black.withAlpha(40),
                              blurRadius: 15,
                              spreadRadius: 1,
                              offset: const Offset(0, -3),
                            ),
                          ]
                        : [
                            BoxShadow(
                              color: Colors.black.withAlpha(25),
                              blurRadius: 10,
                              spreadRadius: 2,
                              offset: const Offset(0, -2),
                            ),
                          ],
                  ),
                  // Fix bottom overflow by adjusting padding
                  padding: EdgeInsets.only(
                    bottom: isKeyboardVisible
                        ? keyboardHeight
                        : 8, // Reduced from 0 to 8 to prevent overflow
                  ),
                  child: Column(
                    mainAxisSize:
                        MainAxisSize.min, // Prevent unnecessary expansion
                    children: [
                      _buildSheetHeader(context),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 6, // Reduced from 8 to 6 to save space
                        ),
                        child: _buildCurvedSearchBar(context),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              bottom: 8), // Add bottom padding to content
                          child: _buildStationListContent(
                            context,
                            scrollController,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  // Enhanced _buildSheetHeader method with improved draggability and theme support
  Widget _buildSheetHeader(BuildContext context) {
    final themeNotifier = ref.watch(themeNotifierProvider.notifier);
    final isDarkMode = themeNotifier.isDarkMode;
    // Make the handle area fully draggable with no extra spacing
    return GestureDetector(
      behavior:
          HitTestBehavior.opaque, // Important: Makes entire area draggable
      onVerticalDragUpdate: (details) {
        if (widget.sheetController.isAttached) {
          // ENHANCED: More responsive drag calculation with sensitivity adjustment
          final screenHeight = MediaQuery.of(context).size.height;
          final sensitivity = 1.2; // Increase sensitivity for smoother control
          final newSize = widget.sheetController.size -
              (details.delta.dy * sensitivity / screenHeight);

          // Clamp the size between min and max
          final clampedSize =
              newSize.clamp(widget.minSheetSize, widget.maxSheetSize);

          // Update the sheet position smoothly
          widget.sheetController.jumpTo(clampedSize);
        }
      },
      onVerticalDragEnd: (details) {
        if (widget.sheetController.isAttached) {
          // ENHANCED: More intelligent snap behavior with better velocity handling
          final currentSize = widget.sheetController.size;
          final velocity = details.velocity.pixelsPerSecond.dy;

          double targetSize;

          // Lower velocity thresholds for more responsive snapping
          if (velocity < -800) {
            // Fast upward swipe - go to max
            targetSize = widget.maxSheetSize;
          } else if (velocity > 800) {
            // Fast downward swipe - go to min
            targetSize = widget.minSheetSize;
          } else {
            // Smart position-based snapping with three zones
            final minToMid = (widget.minSheetSize + widget.midSheetSize) / 2;
            final midToMax = (widget.midSheetSize + widget.maxSheetSize) / 2;

            if (currentSize < minToMid) {
              targetSize = widget.minSheetSize;
            } else if (currentSize < midToMax) {
              targetSize = widget.midSheetSize;
            } else {
              targetSize = widget.maxSheetSize;
            }
          }

          // Animate to the target size with smoother curve
          widget.sheetController.animateTo(
            targetSize,
            duration: const Duration(milliseconds: 250), // Slightly faster
            curve: Curves.easeOutCubic, // Smoother curve
          );
        }
      },
      onTap: () {
        // Toggle between min and max size when tapping the handle
        if (widget.sheetController.isAttached) {
          final currentSize = widget.sheetController.size;
          final targetSize = currentSize < widget.maxSheetSize * 0.7
              ? widget.maxSheetSize
              : widget.minSheetSize;

          // Animate to the target size
          widget.sheetController.animateTo(
            targetSize,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      },
      child: Container(
        width: double.infinity,
        height: 30, // Reduced height to minimize spacing
        color: Colors.transparent,
        alignment: Alignment.center,
        child: Container(
          width: 50,
          height: 5, // Handle indicator
          decoration: BoxDecoration(
            color: isDarkMode
                ? AppThemes.darkTextTertiary.withAlpha(150)
                : Colors.grey.withAlpha(200),
            borderRadius: BorderRadius.circular(2.5),
          ),
        ),
      ),
    );
  }

  // Enhanced _buildCurvedSearchBar method with improved styling and draggability
  Widget _buildCurvedSearchBar(BuildContext context) {
    final themeNotifier = ref.watch(themeNotifierProvider.notifier);
    final isDarkMode = themeNotifier.isDarkMode;
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.8, end: 1.0),
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          // Add GestureDetector to make the search bar area draggable
          child: GestureDetector(
            // This allows dragging the sheet from the search bar area
            // but doesn't interfere with the text field interaction
            onVerticalDragUpdate: (details) {
              // Always handle drag, regardless of text field focus
              if (widget.sheetController.isAttached) {
                // Calculate new sheet size based on drag
                final newSize = widget.sheetController.size -
                    (details.delta.dy / MediaQuery.of(context).size.height);

                // Clamp the size between min and max
                final clampedSize =
                    newSize.clamp(widget.minSheetSize, widget.maxSheetSize);

                // Update the sheet position
                widget.sheetController.jumpTo(clampedSize);

                // If text field has focus, unfocus it during drag
                if (FocusScope.of(context).hasFocus) {
                  FocusScope.of(context).unfocus();
                }
              }
            },
            onVerticalDragEnd: (details) {
              // Always handle drag, regardless of text field focus
              if (widget.sheetController.isAttached) {
                // Determine whether to snap to min or max position
                final velocity = details.velocity.pixelsPerSecond.dy;

                double targetSize;
                if (velocity < -500) {
                  // Fast upward swipe - go to max
                  targetSize = widget.maxSheetSize;
                } else if (velocity > 500) {
                  // Fast downward swipe - go to min
                  targetSize = widget.minSheetSize;
                } else {
                  // Based on current position
                  if (widget.sheetController.size <
                      (widget.minSheetSize + widget.maxSheetSize) / 2) {
                    targetSize = widget.minSheetSize;
                  } else {
                    targetSize = widget.maxSheetSize;
                  }
                }

                // Animate to the target size
                widget.sheetController.animateTo(
                  targetSize,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            },
            // Enhanced modern search bar UI
            child: Container(
              height: 56, // Increased height for better touch targets
              decoration: BoxDecoration(
                // Modern gradient background
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isDarkMode
                      ? [
                          AppThemes.darkCard,
                          AppThemes.darkCard.withAlpha(240),
                        ]
                      : [
                          Colors.white,
                          const Color(0xFFFAFBFF),
                        ],
                ),
                borderRadius: BorderRadius.circular(16), // More rounded corners
                border: Border.all(
                  color: isDarkMode
                      ? AppThemes.darkBorder.withAlpha(100)
                      : AppThemes.primaryColor.withAlpha(30),
                  width: 1.5,
                ),
                boxShadow: [
                  // Enhanced shadow for depth
                  BoxShadow(
                    color: isDarkMode
                        ? Colors.black.withAlpha(40)
                        : AppThemes.primaryColor.withAlpha(15),
                    blurRadius: isDarkMode ? 12 : 8,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                  // Inner highlight for glass effect
                  BoxShadow(
                    color: isDarkMode
                        ? Colors.white.withAlpha(5)
                        : Colors.white.withAlpha(80),
                    blurRadius: 1,
                    spreadRadius: 0,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const SizedBox(
                      width:
                          12), // Reduced padding to move search icon further left
                  // Enhanced search icon with background
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? AppThemes.primaryColor.withAlpha(20)
                          : AppThemes.primaryColor.withAlpha(10),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: widget.isSearchLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2.5,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppThemes.primaryColor,
                                ),
                              ),
                            )
                          : Icon(
                              Icons.search_rounded,
                              color: AppThemes.primaryColor,
                              size: 20,
                            ),
                    ),
                  ),
                  const SizedBox(width: 16), // Increased spacing
                  // Enhanced search text field
                  Expanded(
                    child: GestureDetector(
                      // Add vertical drag gestures to the TextField area
                      onVerticalDragUpdate: (details) {
                        if (widget.sheetController.isAttached) {
                          // Calculate new sheet size based on drag
                          final newSize = widget.sheetController.size -
                              (details.delta.dy /
                                  MediaQuery.of(context).size.height);

                          // Clamp the size between min and max
                          final clampedSize = newSize.clamp(
                              widget.minSheetSize, widget.maxSheetSize);

                          // Update the sheet position
                          widget.sheetController.jumpTo(clampedSize);

                          // Unfocus text field during drag
                          FocusScope.of(context).unfocus();
                        }
                      },
                      onVerticalDragEnd: (details) {
                        if (widget.sheetController.isAttached) {
                          // Determine whether to snap to min or max position
                          final velocity = details.velocity.pixelsPerSecond.dy;

                          double targetSize;
                          if (velocity < -500) {
                            // Fast upward swipe - go to max
                            targetSize = widget.maxSheetSize;
                          } else if (velocity > 500) {
                            // Fast downward swipe - go to min
                            targetSize = widget.minSheetSize;
                          } else {
                            // Based on current position
                            if (widget.sheetController.size <
                                (widget.minSheetSize + widget.maxSheetSize) /
                                    2) {
                              targetSize = widget.minSheetSize;
                            } else {
                              targetSize = widget.maxSheetSize;
                            }
                          }

                          // Animate to the target size
                          widget.sheetController.animateTo(
                            targetSize,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeOut,
                          );
                        }
                      },
                      // This behavior ensures the gesture detector captures all events
                      behavior: HitTestBehavior.translucent,
                      child: TextField(
                        controller: widget.searchController,
                        autofocus: false,
                        decoration: InputDecoration(
                          hintText: 'Search...',
                          hintStyle: TextStyle(
                            color: isDarkMode
                                ? AppThemes.darkTextTertiary
                                : Colors.grey.shade500,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          focusedErrorBorder: InputBorder.none,
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 16),
                        ),
                        style: TextStyle(
                          color: isDarkMode
                              ? AppThemes.darkTextPrimary
                              : AppThemes.lightTextPrimary,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        onChanged: (_) => widget.onSearchChanged(),
                        onTap: () {
                          // Expand sheet when search field is tapped
                          if (widget.sheetController.isAttached) {
                            widget.sheetController.animateTo(
                              widget.maxSheetSize,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeOut,
                            );
                          }
                        },
                      ),
                    ),
                  ),
                  // Enhanced clear button when text is entered
                  if (widget.searchController.text.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(20),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(20),
                          onTap: () {
                            widget.searchController.clear();
                            widget.onSearchChanged();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.grey.shade700.withAlpha(100)
                                  : Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              Icons.close_rounded,
                              size: 16,
                              color: isDarkMode
                                  ? AppThemes.darkTextSecondary
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  // Enhanced divider
                  Container(
                    height: 32,
                    width: 1.5,
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          isDarkMode
                              ? AppThemes.darkBorder
                              : Colors.grey.shade300,
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                  // Enhanced filter button with modern design - icon only, more compact
                  Container(
                    margin: const EdgeInsets.only(
                        right: 8), // Moved further to the right
                    child: Material(
                      color: Colors.transparent,
                      borderRadius:
                          BorderRadius.circular(12), // Slightly smaller radius
                      child: InkWell(
                        onTap: () => widget.showFilterOptions(context),
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding:
                              const EdgeInsets.all(10), // More compact padding
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isDarkMode
                                  ? [
                                      AppThemes.primaryColor.withAlpha(40),
                                      AppThemes.primaryColor.withAlpha(20),
                                    ]
                                  : [
                                      AppThemes.primaryColor.withAlpha(15),
                                      AppThemes.primaryColor.withAlpha(25),
                                    ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppThemes.primaryColor.withAlpha(60),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppThemes.primaryColor.withAlpha(20),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.filter_alt_rounded,
                            color: AppThemes.primaryColor,
                            size: 18, // Slightly larger icon since no text
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Add the missing _buildStationListContent method
  Widget _buildStationListContent(
      BuildContext context, ScrollController scrollController) {
    // Debug logging
    debugPrint(
        'StationListSheet: isLoading=${widget.isLoading}, stations=${widget.stations.length}, filteredStations=${widget.filteredStations.length}, isSearching=${widget.isSearching}, hasApiError=${widget.hasApiError}, errorMessage=${widget.errorMessage}, shouldShowFinalState=$_shouldShowFinalState');

    // Priority 1: Always show loading if we haven't met minimum duration or API is still running
    if (widget.isLoading || !_shouldShowFinalState) {
      debugPrint(
          'StationListSheet: Showing loading animation (minimum duration not met or still loading)');
      return _buildRenderingSheetAnimation(context);
    }

    // Priority 2: Handle API errors only after loading is complete and minimum duration elapsed
    if (widget.hasApiError &&
        widget.errorMessage != null &&
        _shouldShowFinalState) {
      debugPrint('StationListSheet: Showing API error state');
      return _buildMinimalistErrorState(context);
    }

    // Priority 3: Handle empty response only after API completion and minimum duration
    if (!widget.isSearching &&
        widget.stations.isEmpty &&
        _shouldShowFinalState) {
      debugPrint('StationListSheet: Showing empty stations state');
      return _buildMinimalistEmptyState(context);
    }

    // Modern enhanced search loading animation
    if (widget.isSearching && widget.isSearchLoading) {
      final themeNotifier = ref.watch(themeNotifierProvider.notifier);
      final isDarkMode = themeNotifier.isDarkMode;
      final primaryColor = AppThemes.primaryColor;
      final secondaryColor =
          isDarkMode ? AppThemes.darkTextTertiary : Colors.grey.shade300;

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated search progress indicator
            SizedBox(
              height: 80,
              width: 80,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Outer rotating circle
                  TweenAnimationBuilder<double>(
                    // Fix: Use valid ranges for animation curves
                    tween: Tween<double>(begin: 0.0, end: 1.0),
                    duration: const Duration(milliseconds: 1500),
                    curve: Curves.easeInOutCubic,
                    builder: (context, value, child) {
                      // Convert the 0-1 value to radians (0 to 2*pi)
                      return Transform.rotate(
                        angle: value * 2 * 3.14159,
                        child: CircularProgressIndicator(
                          valueColor:
                              AlwaysStoppedAnimation<Color>(primaryColor),
                          strokeWidth: 3,
                        ),
                      );
                    },
                    onEnd: () {
                      if (mounted) setState(() {});
                    },
                  ),

                  // Inner pulsing search icon
                  TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0.8, end: 1.1),
                    duration: const Duration(milliseconds: 800),
                    curve: Curves.easeInOut,
                    builder: (context, value, child) {
                      return Transform.scale(
                        scale: value,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                isDarkMode ? AppThemes.darkCard : Colors.white,
                            shape: BoxShape.circle,
                            border: isDarkMode
                                ? Border.all(
                                    color: AppThemes.darkBorder, width: 1)
                                : null,
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode
                                    ? Colors.black.withAlpha(20)
                                    : Colors.black.withAlpha(10),
                                blurRadius: isDarkMode ? 8 : 4,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.search,
                            size: 28,
                            color: primaryColor,
                          ),
                        ),
                      );
                    },
                    onEnd: () => setState(() {}),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Animated search text
            TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.9, end: 1.0),
              duration: const Duration(milliseconds: 800),
              curve: Curves.easeInOut,
              builder: (context, value, child) {
                return Opacity(
                  opacity: value,
                  child: Text(
                    'Searching for "${widget.searchController.text}"',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode
                          ? AppThemes.darkTextSecondary
                          : AppThemes.lightTextSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
              onEnd: () => setState(() {}),
            ),

            const SizedBox(height: 30),

            // Modern shimmer loading effect for station cards with staggered animation
            ...List.generate(4, (index) {
              // Staggered animation delay based on index
              return Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  duration: Duration(milliseconds: 600 + (index * 100)),
                  curve: Curves.easeOutQuart,
                  builder: (context, value, child) {
                    return Transform.translate(
                      offset: Offset(0, 20 * (1 - value)),
                      child: Opacity(
                        opacity: value,
                        child: Container(
                          height: 100,
                          decoration: BoxDecoration(
                            color:
                                isDarkMode ? AppThemes.darkCard : Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            border: isDarkMode
                                ? Border.all(
                                    color: AppThemes.darkBorder, width: 1)
                                : null,
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode
                                    ? Colors.black.withAlpha(30)
                                    : Colors.black.withAlpha(15),
                                blurRadius: isDarkMode ? 12 : 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              const SizedBox(width: 16),
                              // Connector icon placeholder with shimmer effect
                              TweenAnimationBuilder<double>(
                                tween: Tween<double>(begin: 0.7, end: 0.9),
                                duration: const Duration(milliseconds: 1000),
                                curve: Curves.easeInOut,
                                builder: (context, shimmerValue, _) {
                                  return Container(
                                    width: 60,
                                    height: 60,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          isDarkMode
                                              ? Colors.grey.shade800
                                              : Colors.grey.shade200,
                                          isDarkMode
                                              ? Colors.grey.shade700.withAlpha(
                                                  (shimmerValue * 255).toInt())
                                              : Colors.grey.shade300.withAlpha(
                                                  (shimmerValue * 255).toInt()),
                                          isDarkMode
                                              ? Colors.grey.shade800
                                              : Colors.grey.shade200,
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  );
                                },
                                onEnd: () => setState(() {}),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Title placeholder with shimmer effect
                                    TweenAnimationBuilder<double>(
                                      tween:
                                          Tween<double>(begin: 0.7, end: 0.9),
                                      duration:
                                          const Duration(milliseconds: 1000),
                                      curve: Curves.easeInOut,
                                      builder: (context, shimmerValue, _) {
                                        return Container(
                                          width: 150 +
                                              (index * 10), // Varied widths
                                          height: 16,
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                              colors: [
                                                isDarkMode
                                                    ? Colors.grey.shade800
                                                    : Colors.grey.shade200,
                                                isDarkMode
                                                    ? Colors.grey.shade700
                                                        .withAlpha(
                                                            (shimmerValue * 255)
                                                                .toInt())
                                                    : Colors.grey.shade300
                                                        .withAlpha(
                                                            (shimmerValue * 255)
                                                                .toInt()),
                                                isDarkMode
                                                    ? Colors.grey.shade800
                                                    : Colors.grey.shade200,
                                              ],
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                        );
                                      },
                                      onEnd: () => setState(() {}),
                                    ),
                                    const SizedBox(height: 8),
                                    // Subtitle placeholder with shimmer effect
                                    TweenAnimationBuilder<double>(
                                      tween:
                                          Tween<double>(begin: 0.7, end: 0.9),
                                      duration:
                                          const Duration(milliseconds: 1000),
                                      curve: Curves.easeInOut,
                                      builder: (context, shimmerValue, _) {
                                        return Container(
                                          width: 100 -
                                              (index * 5), // Varied widths
                                          height: 12,
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                              colors: [
                                                isDarkMode
                                                    ? Colors.grey.shade800
                                                    : Colors.grey.shade200,
                                                isDarkMode
                                                    ? Colors.grey.shade700
                                                        .withAlpha(
                                                            (shimmerValue * 255)
                                                                .toInt())
                                                    : Colors.grey.shade300
                                                        .withAlpha(
                                                            (shimmerValue * 255)
                                                                .toInt()),
                                                isDarkMode
                                                    ? Colors.grey.shade800
                                                    : Colors.grey.shade200,
                                              ],
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                        );
                                      },
                                      onEnd: () => setState(() {}),
                                    ),
                                    const SizedBox(height: 8),
                                    // Status indicator placeholder
                                    Row(
                                      children: [
                                        Container(
                                          width: 8,
                                          height: 8,
                                          decoration: BoxDecoration(
                                            color: index % 2 == 0
                                                ? primaryColor.withAlpha(150)
                                                : secondaryColor,
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        const SizedBox(width: 6),
                                        TweenAnimationBuilder<double>(
                                          tween: Tween<double>(
                                              begin: 0.7, end: 0.9),
                                          duration: const Duration(
                                              milliseconds: 1000),
                                          curve: Curves.easeInOut,
                                          builder: (context, shimmerValue, _) {
                                            return Container(
                                              width: 60,
                                              height: 8,
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: [
                                                    isDarkMode
                                                        ? Colors.grey.shade800
                                                        : Colors.grey.shade200,
                                                    isDarkMode
                                                        ? Colors.grey.shade700
                                                            .withAlpha(
                                                                (shimmerValue *
                                                                        255)
                                                                    .toInt())
                                                        : Colors.grey.shade300
                                                            .withAlpha(
                                                                (shimmerValue *
                                                                        255)
                                                                    .toInt()),
                                                    isDarkMode
                                                        ? Colors.grey.shade800
                                                        : Colors.grey.shade200,
                                                  ],
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                            );
                                          },
                                          onEnd: () => setState(() {}),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              // Distance indicator placeholder
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  TweenAnimationBuilder<double>(
                                    tween: Tween<double>(begin: 0.7, end: 0.9),
                                    duration:
                                        const Duration(milliseconds: 1000),
                                    curve: Curves.easeInOut,
                                    builder: (context, shimmerValue, _) {
                                      return Container(
                                        width: 40,
                                        height: 24,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                            colors: [
                                              isDarkMode
                                                  ? Colors.grey.shade800
                                                  : Colors.grey.shade200,
                                              isDarkMode
                                                  ? Colors.grey.shade700
                                                      .withAlpha(
                                                          (shimmerValue * 255)
                                                              .toInt())
                                                  : Colors.grey.shade300
                                                      .withAlpha(
                                                          (shimmerValue * 255)
                                                              .toInt()),
                                              isDarkMode
                                                  ? Colors.grey.shade800
                                                  : Colors.grey.shade200,
                                            ],
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                      );
                                    },
                                    onEnd: () => setState(() {}),
                                  ),
                                ],
                              ),
                              const SizedBox(width: 16),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              );
            }),
          ],
        ),
      );
    }

    // Show search loading animation when user is typing
    if (widget.isSearching && widget.searchController.text.isNotEmpty) {
      // First check if we're in the loading state
      if (widget.isSearchLoading || widget.filteredStations.isEmpty) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        final primaryColor = const Color(0xFF67C44C);

        // If we're still loading, show the loading animation
        if (widget.isSearchLoading) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Animated search progress indicator
                SizedBox(
                  height: 80,
                  width: 80,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Outer rotating circle
                      TweenAnimationBuilder<double>(
                        // Fix: Use valid ranges for animation curves
                        tween: Tween<double>(begin: 0.0, end: 1.0),
                        duration: const Duration(milliseconds: 1500),
                        curve: Curves.easeInOutCubic,
                        builder: (context, value, child) {
                          // Convert the 0-1 value to radians (0 to 2*pi)
                          return Transform.rotate(
                            angle: value * 2 * 3.14159,
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(primaryColor),
                              strokeWidth: 3,
                            ),
                          );
                        },
                        onEnd: () {
                          if (mounted) setState(() {});
                        },
                      ),

                      // Inner pulsing search icon
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.8, end: 1.1),
                        duration: const Duration(milliseconds: 800),
                        curve: Curves.easeInOut,
                        builder: (context, value, child) {
                          return Transform.scale(
                            scale: value,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? const Color(0xFF2A2A2A)
                                    : Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(10),
                                    blurRadius: 4,
                                    spreadRadius: 1,
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.search,
                                size: 28,
                                color: primaryColor,
                              ),
                            ),
                          );
                        },
                        onEnd: () => setState(() {}),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Animated search text
                TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.9, end: 1.0),
                  duration: const Duration(milliseconds: 800),
                  curve: Curves.easeInOut,
                  builder: (context, value, child) {
                    return Opacity(
                      opacity: value,
                      child: Text(
                        'Searching for "${widget.searchController.text}"',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: isDarkMode
                              ? Colors.grey.shade300
                              : Colors.grey.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    );
                  },
                  onEnd: () => setState(() {}),
                ),

                const SizedBox(height: 30),

                // Modern shimmer loading effect for station cards with staggered animation
                ...List.generate(4, (index) {
                  // Staggered animation delay based on index
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0.0, end: 1.0),
                      duration: Duration(milliseconds: 600 + (index * 100)),
                      curve: Curves.easeOutQuart,
                      builder: (context, value, child) {
                        return Transform.translate(
                          offset: Offset(0, 20 * (1 - value)),
                          child: Opacity(
                            opacity: value,
                            child: Container(
                              height: 100,
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? const Color(0xFF1E1E1E)
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black
                                        .withAlpha(isDarkMode ? 30 : 15),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  const SizedBox(width: 16),
                                  // Connector icon placeholder with shimmer effect
                                  TweenAnimationBuilder<double>(
                                    tween: Tween<double>(begin: 0.7, end: 0.9),
                                    duration:
                                        const Duration(milliseconds: 1000),
                                    curve: Curves.easeInOut,
                                    builder: (context, shimmerValue, _) {
                                      return Container(
                                        width: 60,
                                        height: 60,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                            colors: [
                                              isDarkMode
                                                  ? Colors.grey.shade800
                                                  : Colors.grey.shade200,
                                              isDarkMode
                                                  ? Colors.grey.shade700
                                                      .withAlpha(
                                                          (shimmerValue * 255)
                                                              .toInt())
                                                  : Colors.grey.shade300
                                                      .withAlpha(
                                                          (shimmerValue * 255)
                                                              .toInt()),
                                              isDarkMode
                                                  ? Colors.grey.shade800
                                                  : Colors.grey.shade200,
                                            ],
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                      );
                                    },
                                    onEnd: () => setState(() {}),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        // Title placeholder with shimmer effect
                                        TweenAnimationBuilder<double>(
                                          tween: Tween<double>(
                                              begin: 0.7, end: 0.9),
                                          duration: const Duration(
                                              milliseconds: 1000),
                                          curve: Curves.easeInOut,
                                          builder: (context, shimmerValue, _) {
                                            return Container(
                                              width: 150 +
                                                  (index * 10), // Varied widths
                                              height: 16,
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: [
                                                    isDarkMode
                                                        ? Colors.grey.shade800
                                                        : Colors.grey.shade200,
                                                    isDarkMode
                                                        ? Colors.grey.shade700
                                                            .withAlpha(
                                                                (shimmerValue *
                                                                        255)
                                                                    .toInt())
                                                        : Colors.grey.shade300
                                                            .withAlpha(
                                                                (shimmerValue *
                                                                        255)
                                                                    .toInt()),
                                                    isDarkMode
                                                        ? Colors.grey.shade800
                                                        : Colors.grey.shade200,
                                                  ],
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                            );
                                          },
                                          onEnd: () => setState(() {}),
                                        ),
                                        const SizedBox(height: 8),
                                        // Subtitle placeholder with shimmer effect
                                        TweenAnimationBuilder<double>(
                                          tween: Tween<double>(
                                              begin: 0.7, end: 0.9),
                                          duration: const Duration(
                                              milliseconds: 1000),
                                          curve: Curves.easeInOut,
                                          builder: (context, shimmerValue, _) {
                                            return Container(
                                              width: 100 -
                                                  (index * 5), // Varied widths
                                              height: 12,
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: [
                                                    isDarkMode
                                                        ? Colors.grey.shade800
                                                        : Colors.grey.shade200,
                                                    isDarkMode
                                                        ? Colors.grey.shade700
                                                            .withAlpha(
                                                                (shimmerValue *
                                                                        255)
                                                                    .toInt())
                                                        : Colors.grey.shade300
                                                            .withAlpha(
                                                                (shimmerValue *
                                                                        255)
                                                                    .toInt()),
                                                    isDarkMode
                                                        ? Colors.grey.shade800
                                                        : Colors.grey.shade200,
                                                  ],
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  );
                }),
              ],
            ),
          );
        }

        // If we're not loading anymore but have no results, show the "No stations found" message
        if (widget.filteredStations.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Static empty state icon (removed pulsing animation)
                Icon(
                  Icons.search_off,
                  size: 64,
                  color:
                      isDarkMode ? Colors.grey.shade500 : Colors.grey.shade400,
                ),
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    'No stations found matching "${widget.searchController.text}"',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode
                          ? Colors.grey.shade300
                          : Colors.grey.shade700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    'Try a different search term or explore the map to find stations',
                    style: TextStyle(
                      fontSize: 14,
                      color: isDarkMode
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    widget.searchController.clear();
                    widget.onSearchChanged();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF67C44C),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: const Text('Clear Search'),
                ),
              ],
            ),
          );
        }
      }
    }

    // Handle empty stations list (not due to search)
    debugPrint(
        'StationListSheet: stations list has ${widget.stations.length} stations');
    if (!widget.isSearching && widget.stations.isEmpty) {
      // Show loading animation instead of "No charging stations found" message
      return _buildRenderingSheetAnimation(context);
    }

    // If we've reached here, we need to show the station list
    final stationsList =
        widget.isSearching ? widget.filteredStations : widget.stations;

    debugPrint(
        'StationListSheet: displaying ${stationsList.length} stations in the list');

    // Create a stack with the list and a loading overlay if needed
    return Stack(
      children: [
        // Main station list
        _buildStationListView(stationsList, scrollController),

        // Loading overlay that shows on top of the list when loading
        if (widget.isLoading && widget.stations.isNotEmpty)
          Container(
            color: Colors.white.withAlpha(179), // ~0.7 opacity
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                          const Color(0xFF67C44C)),
                      strokeWidth: 3,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Updating stations...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  // Simplified loading animation for loading state
  Widget _buildRenderingSheetAnimation(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Simple loading circle animation
          SizedBox(
            width: 50,
            height: 50,
            child: CircularProgressIndicator(
              valueColor:
                  AlwaysStoppedAnimation<Color>(const Color(0xFF67C44C)),
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: 24),

          // Loading text with dynamic message
          Text(
            _getLoadingMessage(),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),

          // Shimmer loading effect for station cards
          ...List.generate(3, (index) {
            // Add a small delay for each card to create a wave effect
            return Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: 1.0),
                duration: const Duration(milliseconds: 600),
                curve: Curves.easeInOut,
                builder: (context, value, child) {
                  return Opacity(
                    opacity: value,
                    child: Container(
                      height: 100,
                      decoration: BoxDecoration(
                        color:
                            isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(15),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          const SizedBox(width: 16),
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.grey.shade800
                                  : Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 150,
                                  height: 16,
                                  decoration: BoxDecoration(
                                    color: isDarkMode
                                        ? Colors.grey.shade800
                                        : Colors.grey.shade200,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  width: 100,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: isDarkMode
                                        ? Colors.grey.shade800
                                        : Colors.grey.shade200,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          }),

          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Helper method to build the station list view
  Widget _buildStationListView(List<Map<String, dynamic>> stationsList,
      ScrollController scrollController) {
    debugPrint(
        'StationListSheet: Building ListView with ${stationsList.length} stations');

    // Removed the GestureDetector wrapper to avoid conflicts with the DraggableScrollableSheet
    // Removed RefreshIndicator - refresh functionality only available through map controls
    return ListView.builder(
      controller: scrollController,
      // Remove bottom padding to eliminate empty space
      padding: const EdgeInsets.only(bottom: 0),
      physics: const BouncingScrollPhysics(
          parent:
              AlwaysScrollableScrollPhysics()), // Enhanced physics for better scrolling feel
      itemCount: stationsList.length + 1, // +1 for the header
      itemBuilder: (context, index) {
        if (index == 0) {
          // Header
          debugPrint(
              'StationListSheet: Building header for ${widget.isSearching ? "search results" : "nearby stations"}');
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                Text(
                  widget.isSearching
                      ? 'Search Results (${stationsList.length})'
                      : 'Nearby Stations',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                // Only show refresh button when not loading
              ],
            ),
          );
        }

        // Station card with animation
        final station = stationsList[index - 1];
        debugPrint(
            'StationListSheet: Building station card for station ${station['name']}');

        // Return station card directly without scaling animation
        return widget.buildStationCard(station);
      },
    );
  }

  // Get dynamic loading message based on current state
  String _getLoadingMessage() {
    if (widget.isLoading) {
      return 'Finding nearby stations...';
    } else if (!_hasMinimumLoadingElapsed) {
      return 'Preparing results...';
    } else {
      return 'Loading stations...';
    }
  }

  // Minimalist error state for API failures
  Widget _buildMinimalistErrorState(BuildContext context) {
    final themeNotifier = ref.watch(themeNotifierProvider.notifier);
    final isDarkMode = themeNotifier.isDarkMode;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
            ),
            const SizedBox(height: 16),
            Text(
              widget.errorMessage ?? 'Unable to load stations',
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode
                    ? AppThemes.darkTextSecondary
                    : AppThemes.lightTextSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (widget.onRetry != null) ...[
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: widget.onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppThemes.primaryColor,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Minimalist empty state for no stations found
  Widget _buildMinimalistEmptyState(BuildContext context) {
    final themeNotifier = ref.watch(themeNotifierProvider.notifier);
    final isDarkMode = themeNotifier.isDarkMode;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 48,
              color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
            ),
            const SizedBox(height: 16),
            Text(
              'No charging stations found nearby',
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode
                    ? AppThemes.darkTextSecondary
                    : AppThemes.lightTextSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Try a different location or expand search area',
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode
                    ? AppThemes.darkTextTertiary
                    : Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            if (widget.onExpandSearchRadius != null) ...[
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: widget.onExpandSearchRadius,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppThemes.primaryColor,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Expand Search'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
