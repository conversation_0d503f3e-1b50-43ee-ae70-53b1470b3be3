import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../components/station_list_sheet.dart';
import '../../providers/dashboard_notifier.dart';
import '../../repositories/station_repository.dart';
import '../../services/api_bridge.dart';
import '../../core/api/api_service.dart'; // Use centralized API service
import '../../services/background_refresh_service.dart';
import '../station/station_details_page.dart';
import '../station/station_list_page.dart';
import 'filter_dialog.dart';
import 'google_map_widget.dart';
import '../../features/station/services/station_service.dart';
import '../../services/persistent_marker_service.dart';
import '../../core/services/connectivity_service.dart';
import '../../services/location_service.dart';
import '../../services/ongoing_sessions_service.dart';

// DashboardScreen widget definition
class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  DashboardScreenState createState() => DashboardScreenState();
}

class DashboardScreenState extends ConsumerState<DashboardScreen>
    with TickerProviderStateMixin {
  /// Set this flag to false to use your MapWidget (map.html via WebView)
  /// Set to true to use a placeholder instead of the map
  final bool useMapPlaceholder = false;

  // Use PersistentMarkerService for reliable marker handling
  final PersistentMarkerService _persistentMarkerService =
      PersistentMarkerService();

  // Simple UID validation

  // Default position (center of India for initial display)
  static const double _indiaLatitude = 20.5937;
  static const double _indiaLongitude = 78.9629;
  static const double _indiaZoom = 5.0;

  // User's actual location (obtained in background)
  double? _currentLatitude;
  double? _currentLongitude;

  // Single variable to store user activity location
  LatLng? _userActivityLocation;

  // Flag to track if we've received a location update
  bool _hasLocationUpdate = false;

  // Method to handle location updates from the map
  void _onLocationUpdated(double latitude, double longitude) {
    debugPrint('Location updated: Lat $latitude, Lng $longitude');

    // Update the current location immediately
    setState(() {
      _currentLatitude = latitude;
      _currentLongitude = longitude;
      _hasLocationUpdate = true;
      // Clear any location error messages when we receive location updates
      _errorMessageLocation = null;
    });

    // Only trigger automatic location updates for the initial location display
    // Subsequent updates should not trigger automatic API calls or map movements
    if (!_isDashboardInitialized) {
      // Schedule the Riverpod state update after the current frame
      // This ensures the UI updates first and state update happens separately
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _safelyUpdateLocation();
      });
    } else {
      debugPrint(
          '🗺️ Dashboard already initialized, skipping automatic location update');
    }

    // We don't automatically load nearest stations when location updates
    // Only load them when the user explicitly taps the location button
  }

  // Flag to track if we're loading nearest stations
  bool _isLoadingNearestStations = false;
  // Flag to show nearest stations instead of all stations
  bool _showingNearestStations = false;
  // Flag to track if data is being refreshed
  bool _isRefreshing = false;

  // Selected station data
  Map<String, dynamic>? selectedStation;

  // Background refresh service
  final BackgroundRefreshService _backgroundRefreshService =
      BackgroundRefreshService();
  bool _showConnectorSheet = false;
  final DraggableScrollableController _sheetController =
      DraggableScrollableController();
  // DraggableScrollableSheet sizes
  final double _minSheetSize =
      0.28; // Optimized to keep search bar visible above navigation bar while showing more map
  final double _midSheetSize = 0.5; // Add mid-size for partially expanded sheet
  final double _maxSheetSize = 0.9;

  // Loading states for UI
  bool _isLoadingStations = false;
  bool _isLoadingLocation = false; // Used in safelyUpdateLocation

  // Error messages
  String? _errorMessage;
  String? _errorMessageLocation;

  // Map controller reference
  GoogleMapController? _mapController;
  // Track the current camera position - will be set when user location is available
  CameraPosition? _lastCameraPosition;

  // Global key for the GoogleMapWidget
  final GlobalKey<GoogleMapWidgetState> _googleMapKey =
      GlobalKey<GoogleMapWidgetState>();

  // Marker for user activity location
  Marker? _userActivityMarker;

  late AnimationController _mapControlsAnimationController;
  late Animation<Offset> _mapControlsAnimation;

  late AnimationController _stationListAnimationController;
  late Animation<double> _stationListAnimation;

  // Refresh button animation controllers
  late AnimationController _refreshAnimationController;
  late Animation<double> _refreshAnimation;
  bool _isRefreshAnimating = false;

  // Battery pulsing animation controllers (separate from refresh)
  late AnimationController _batteryPulseAnimationController;
  late Animation<double> _batteryPulseAnimation;

  // Separate data collections for map and sheet
  List<Map<String, dynamic>> _allMapStations = []; // ALL stations for the map
  List<Map<String, dynamic>> _nearestStations =
      []; // Nearest stations for the sheet
  List<Map<String, dynamic>> _displayStations =
      []; // Currently displayed stations in the sheet

  // Markers will be used when implementing the map functionality
  final TextEditingController _searchController = TextEditingController();
  Timer? _searchDebounce;
  bool _isSearching = false;
  bool _isSearchLoading = false;
  List<Map<String, dynamic>> _filteredStations = [];

  // Ongoing charging sessions state
  final OngoingSessionsService _ongoingSessionsService =
      OngoingSessionsService();
  bool _hasActiveSessions = false;
  bool _isCheckingActiveSessions = false;
  Timer? _ongoingSessionsTimer;

  // Helper method to safely update location in background - Used at startup and when user clicks target button
  // This method updates the location and can optionally call the nearest stations API
  Future<void> _safelyUpdateLocation({bool loadNearestStations = false}) async {
    try {
      // Don't show loading state - work silently in background
      debugPrint('🌍 Getting user location in background...');

      // Get location using the LocationSettings for the newer API
      var position = await geo.Geolocator.getCurrentPosition(
          locationSettings: const geo.LocationSettings(
        accuracy: geo.LocationAccuracy.high,
        timeLimit: Duration(seconds: 5),
      ));

      setState(() {
        _currentLatitude = position.latitude;
        _currentLongitude = position.longitude;
        _hasLocationUpdate = true;
        _errorMessageLocation = null;
        // Update the user activity location
        _userActivityLocation = LatLng(position.latitude, position.longitude);
      });

      debugPrint(
          '🌍 User location obtained: ${position.latitude}, ${position.longitude}');

      // Automatically fetch nearest stations based on user location
      if (loadNearestStations) {
        debugPrint(
            '📍 Automatically loading nearest stations for user location...');
        await _loadNearestStations();

        // Trigger auto-expansion of sheet with nearest stations data after a brief delay
        // This ensures the state has been updated with the new stations
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Future.delayed(const Duration(milliseconds: 300), () {
            if (mounted && context.mounted) {
              _triggerSheetAutoExpansionWithStations();
            }
          });
        });
      }

      // Only smoothly transition map to user location on initial load
      if (_googleMapKey.currentState != null && !_isDashboardInitialized) {
        // Wait a moment for stations to load and sheet to populate
        await Future.delayed(const Duration(milliseconds: 2000));

        if (mounted && _googleMapKey.currentState != null) {
          debugPrint(
              '🗺️ Initial load: Smoothly transitioning to user location...');
          _googleMapKey.currentState!.focusOnLocation(
            position.latitude,
            position.longitude,
            12.0, // Zoom to user location
          );
        }
      } else if (_isDashboardInitialized) {
        debugPrint(
            '🗺️ Dashboard already initialized, skipping automatic map transition');
      }
    } catch (error) {
      debugPrint('🌍 Could not get user location: $error');
      setState(() {
        _errorMessageLocation = "Could not get location: $error";
      });

      // If we couldn't get the user's location and need stations, use default location
      if (loadNearestStations) {
        debugPrint('Using default location for nearest stations');
        await _loadNearestStationsWithDefaultLocation();
      }
    }
  }

  // Initialize repository through constructor
  final StationRepository _stationRepository;

  DashboardScreenState() : _stationRepository = StationRepository();

  // Default search radius for nearest stations API
  final double _searchRadius = 50.0; // Default 50km radius
  // Maximum number of retries for API calls
  final int _maxRetryAttempts = 3;

  @override
  void initState() {
    super.initState();
    // Check if background refresh service is already refreshing
    _isRefreshing = _backgroundRefreshService.isRefreshing();

    // FIXED: Disable background refresh service to prevent bubble effects
    // The background refresh service can cause periodic data updates that trigger UI changes
    // We'll handle refreshing manually through user actions instead
    debugPrint(
        '🚫 Background refresh service disabled to prevent bubble effects');

    // Clear search state when dashboard initializes to prevent persistent search keywords
    _clearDashboardSearchState();

    // Initialize the PersistentMarkerService for reliable marker handling
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _persistentMarkerService.initialize();
    });

    // Setup animations for map controls and station list
    _mapControlsAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );

    _mapControlsAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // Start from below (hidden)
      end: Offset.zero, // End at normal position (visible)
    ).animate(CurvedAnimation(
      parent: _mapControlsAnimationController,
      curve: Curves.easeOut,
    ));

    _stationListAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );

    _stationListAnimation = Tween<double>(
      begin: 0.0, // Start hidden
      end: 1.0, // End fully visible
    ).animate(CurvedAnimation(
      parent: _stationListAnimationController,
      curve: Curves.easeOut,
    ));

    // Initialize refresh animation controller
    _refreshAnimationController = AnimationController(
      vsync: this,
      duration:
          const Duration(milliseconds: 1500), // 1.5 seconds for full rotation
    );

    _refreshAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0, // Full rotation (will be multiplied by 2π)
    ).animate(CurvedAnimation(
      parent: _refreshAnimationController,
      curve: Curves.easeInOut,
    ));

    // Initialize battery pulse animation controller (separate from refresh)
    _batteryPulseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000), // 1 second pulse
    );

    _batteryPulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _batteryPulseAnimationController,
      curve: Curves.easeInOut,
    ));

    debugPrint('Preloading marker images and station data...');

    // Initialize data loading in a post-frame callback to avoid async in initState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureRefreshAnimationStopped(); // Ensure clean animation state
      _initializeDashboardWithStateManagement();
      _checkOngoingSessions(); // Check for active charging sessions
      _startPeriodicSessionsCheck(); // Start periodic checking
    });

    // Start animations and add listeners (single initialization)
    _mapControlsAnimationController.forward();
    _stationListAnimationController.forward();
    _searchController.addListener(_onSearchChanged);

    // FIXED: Auto-expand sheet when dashboard opens with stations
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Start checking for stations immediately and keep checking until we have data
      _checkAndAutoExpandSheet();

      // Add listener to track sheet position changes
      _sheetController.addListener(_onSheetPositionChanged);
    });
  }

  /// Clear dashboard search state to prevent persistent search keywords
  void _clearDashboardSearchState() {
    try {
      // Clear the search controller
      _searchController.clear();

      // Reset search state variables
      if (mounted) {
        setState(() {
          _isSearching = false;
          _isSearchLoading = false;
          _filteredStations.clear();
        });
      }

      debugPrint(
          '🔍 Cleared dashboard search state to prevent persistent keywords');
    } catch (e) {
      debugPrint('❌ Error clearing dashboard search state: $e');
    }
  }

  // Flag to track if initial auto-expand has been completed
  bool _hasInitialAutoExpandCompleted = false;

  // Flag to track if auto-expansion after location has been completed
  bool _hasLocationBasedAutoExpandCompleted = false;

  // Flag to track if dashboard has been initialized to prevent re-initialization
  bool _isDashboardInitialized = false;

  /// Check for stations and auto-expand sheet when data is available (ONLY on initial load)
  void _checkAndAutoExpandSheet() {
    // FIXED: Prevent bubble effect by only allowing one-time auto-expand
    if (_hasInitialAutoExpandCompleted) {
      debugPrint(
          '🚀 Initial auto-expand already completed, skipping to prevent bubble effect');
      return;
    }

    // Check immediately
    if (_sheetController.isAttached && mounted && _displayStations.isNotEmpty) {
      debugPrint('🚀 Auto-expanding station list sheet immediately');
      debugPrint('🚀 Display stations count: ${_displayStations.length}');

      _sheetController.animateTo(
        _midSheetSize,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOutCubic,
      );
      _hasInitialAutoExpandCompleted = true; // Mark as completed
      return;
    }

    // If no stations yet, check only once after a short delay instead of periodic checking
    // This prevents the bubble effect while still allowing initial expansion
    Timer(const Duration(milliseconds: 1000), () {
      if (!mounted || _hasInitialAutoExpandCompleted) return;

      if (_sheetController.isAttached && _displayStations.isNotEmpty) {
        debugPrint('🚀 Auto-expanding station list sheet after 1 second delay');
        debugPrint('🚀 Display stations count: ${_displayStations.length}');

        _sheetController.animateTo(
          _midSheetSize,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOutCubic,
        );
        _hasInitialAutoExpandCompleted = true; // Mark as completed
      } else {
        debugPrint(
            '🚀 No stations available after 1 second, skipping auto-expand');
        _hasInitialAutoExpandCompleted =
            true; // Mark as completed to prevent further attempts
      }
    });
  }

  /// Trigger auto-expansion of sheet when nearest stations are loaded after user location is obtained
  void _triggerSheetAutoExpansionWithStations() {
    // Only auto-expand if we haven't done location-based expansion yet
    if (_hasLocationBasedAutoExpandCompleted) {
      debugPrint('🚀 Location-based auto-expand already completed, skipping');
      return;
    }

    // Auto-expand the sheet to show nearest stations (or no results message)
    if (_sheetController.isAttached && mounted) {
      if (_displayStations.isNotEmpty) {
        debugPrint(
            '🚀 Auto-expanding sheet with ${_displayStations.length} nearest stations');
      } else {
        debugPrint(
            '🚀 Auto-expanding sheet to show "no stations found" message');
      }

      _sheetController.animateTo(
        _midSheetSize,
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeOutCubic,
      );

      _hasLocationBasedAutoExpandCompleted = true; // Mark as completed
    }
  }

  // Handle map tap - only log the tap, don't minimize the sheet
  // This allows users to explore the map without closing the sheet
  void _handleMapTapLocation(LatLng position) {
    debugPrint('Map tapped at: ${position.latitude}, ${position.longitude}');
    // We no longer minimize the sheet when the map is tapped
    // This allows users to view the map while keeping the station list visible

    // Only unfocus text fields to dismiss keyboard if it's visible
    if (MediaQuery.of(context).viewInsets.bottom > 0) {
      FocusScope.of(context).unfocus();
    }
  }

  /// Check for ongoing charging sessions
  Future<void> _checkOngoingSessions() async {
    if (_isCheckingActiveSessions) {
      return; // Prevent multiple simultaneous checks
    }

    setState(() {
      _isCheckingActiveSessions = true;
    });

    try {
      debugPrint('🔋 ===== CHECKING FOR ONGOING CHARGING SESSIONS =====');

      final hasActiveSessions =
          await _ongoingSessionsService.hasOngoingSessions();

      if (mounted) {
        setState(() {
          _hasActiveSessions = hasActiveSessions;
          _isCheckingActiveSessions = false;
        });

        // Start pulsing animation if there are active sessions
        if (hasActiveSessions) {
          _startBatteryPulsingAnimation();
        }
      }

      debugPrint('🔋 Has active sessions: $hasActiveSessions');
    } catch (e) {
      debugPrint('❌ Error checking ongoing sessions: $e');
      if (mounted) {
        setState(() {
          _hasActiveSessions = false;
          _isCheckingActiveSessions = false;
        });
      }
    }
  }

  /// Navigate to active sessions screen
  void _navigateToActiveSessions() {
    Navigator.pushNamed(context, '/active-sessions');
  }

  /// Start pulsing animation for battery icon
  void _startBatteryPulsingAnimation() {
    if (!_batteryPulseAnimationController.isAnimating) {
      _batteryPulseAnimationController.repeat(reverse: true);
    }
  }

  /// Stop pulsing animation for battery icon
  void _stopBatteryPulsingAnimation() {
    if (_batteryPulseAnimationController.isAnimating) {
      _batteryPulseAnimationController.stop();
      _batteryPulseAnimationController.reset();
    }
  }

  /// Start periodic checking for ongoing sessions
  void _startPeriodicSessionsCheck() {
    // Cancel any existing timer
    _ongoingSessionsTimer?.cancel();

    // Check every 30 seconds
    _ongoingSessionsTimer =
        Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      _checkOngoingSessions();
    });
  }

  // Initialize dashboard with comprehensive state management
  Future<void> _initializeDashboardWithStateManagement() async {
    // Prevent re-initialization if already initialized
    if (_isDashboardInitialized) {
      debugPrint(
          '🚀 DASHBOARD ALREADY INITIALIZED - Skipping re-initialization');
      return;
    }

    debugPrint('\n=== INITIALIZING DASHBOARD WITH STATE MANAGEMENT ===');

    final dashboardState = ref.read(dashboardNotifierProvider);
    final locationService = LocationService();

    // Check if this is a return navigation (data already loaded and fresh)
    if (dashboardState.dataLoaded &&
        ref.read(dashboardNotifierProvider.notifier).isDataFresh() &&
        !dashboardState.isInitialLoad) {
      debugPrint('🚀 RETURNING TO DASHBOARD - Using cached data');

      // Restore cached data immediately
      if (mounted) {
        setState(() {
          _allMapStations = dashboardState.allMapMarkers;
          _displayStations = dashboardState.formattedNearestStations;
          _nearestStations = dashboardState.formattedNearestStations;
          _isLoadingStations = false;
          _isLoadingNearestStations = false;
        });
      }

      // Restore camera position if available
      if (dashboardState.lastCameraPosition.target.latitude != 0.0 &&
          _googleMapKey.currentState != null) {
        debugPrint(
            '🗺️ Restoring camera position: ${dashboardState.lastCameraPosition.target}');
        _googleMapKey.currentState!.focusOnLocation(
          dashboardState.lastCameraPosition.target.latitude,
          dashboardState.lastCameraPosition.target.longitude,
          dashboardState.lastCameraPosition.zoom,
        );
      }

      // Restore sheet position
      if (_sheetController.isAttached) {
        _sheetController.animateTo(
          dashboardState.sheetPosition,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }

      // Mark dashboard as initialized
      _isDashboardInitialized = true;
      return; // Exit early - no need to fetch data
    }

    // This is initial load or data is stale - proceed with full initialization
    debugPrint('🚀 INITIAL LOAD OR STALE DATA - Fetching fresh data');

    // Mark as no longer initial load after first time
    if (dashboardState.isInitialLoad) {
      ref.read(dashboardNotifierProvider.notifier).markAsNotInitialLoad();
    }

    // Load map markers first (for India view)
    await _loadMapMarkers();

    // Check if location is already available
    if (locationService.isLocationInitialized()) {
      final lastLocation = locationService.getLastKnownLocation();
      if (lastLocation != null) {
        debugPrint(
            '🗺️ Using cached location: ${lastLocation.latitude}, ${lastLocation.longitude}');

        // Update state with cached location
        if (mounted) {
          setState(() {
            _currentLatitude = lastLocation.latitude;
            _currentLongitude = lastLocation.longitude;
          });
        }

        // Focus map on cached location
        if (_googleMapKey.currentState != null) {
          _googleMapKey.currentState!.focusOnLocation(
            lastLocation.latitude,
            lastLocation.longitude,
            15.0,
          );
        }

        // Load nearest stations with cached location
        await _loadNearestStations();

        // Mark dashboard as initialized
        _isDashboardInitialized = true;
        return;
      }
    }

    // No cached location available - get fresh location
    debugPrint('🗺️ No cached location - fetching fresh location');
    await _safelyUpdateLocation(loadNearestStations: true);

    // Mark dashboard as initialized
    _isDashboardInitialized = true;
  }

  // Helper methods for refresh animation
  void _startRefreshAnimation() {
    if (!_isRefreshAnimating && mounted) {
      setState(() {
        _isRefreshAnimating = true;
      });
      _refreshAnimationController.repeat(); // Continuous rotation
      debugPrint('🔄 Started refresh animation');
    }
  }

  void _stopRefreshAnimation() {
    if (_isRefreshAnimating && mounted) {
      _refreshAnimationController.stop();
      _refreshAnimationController.reset();
      setState(() {
        _isRefreshAnimating = false;
        _isLoadingStations = false;
        _isLoadingNearestStations =
            false; // Ensure nearest stations loading state is reset
        _isRefreshing = false; // Ensure refreshing state is reset
      });
      debugPrint('🔄 Stopped refresh animation');
    } else if (_refreshAnimationController.isAnimating) {
      // Force stop animation even if state is inconsistent
      _refreshAnimationController.stop();
      _refreshAnimationController.reset();
      debugPrint('🔄 Force stopped refresh animation (state inconsistency)');
    }
  }

  // Method to check and fix animation state inconsistencies
  void _ensureRefreshAnimationStopped() {
    if (_refreshAnimationController.isAnimating && !_isRefreshAnimating) {
      debugPrint('🔄 Detected animation state inconsistency - fixing');
      _refreshAnimationController.stop();
      _refreshAnimationController.reset();
    }
  }

  // This method will be used when implementing the search functionality

  @override
  void dispose() {
    // CRITICAL FIX: Stop refresh animation before disposing controllers
    if (_refreshAnimationController.isAnimating) {
      _refreshAnimationController.stop();
      _refreshAnimationController.reset();
      debugPrint('🔄 Stopped refresh animation during dispose');
    }

    _mapControlsAnimationController.dispose();
    _stationListAnimationController.dispose();
    _refreshAnimationController.dispose();
    _batteryPulseAnimationController.dispose();
    _searchController.dispose();
    _sheetController.removeListener(_onSheetPositionChanged);

    // Cancel ongoing sessions timer
    _ongoingSessionsTimer?.cancel();

    // Clear search state when disposing
    _clearDashboardSearchState();

    // Don't dispose the background refresh service or PersistentMarkerService
    // as they are singletons and might be needed elsewhere
    super.dispose();
  }

  void _onSearchChanged() {
    // Cancel previous timer if it exists
    if (_searchDebounce?.isActive ?? false) {
      _searchDebounce!.cancel();
    }

    if (_searchController.text.isEmpty) {
      setState(() {
        _isSearching = false;
        _filteredStations = [];
      });
      return;
    }

    // Set searching and loading state immediately
    setState(() {
      _isSearching = true;
      _isSearchLoading = true;
    });

    // Use a shorter debounce time for more responsive search
    _searchDebounce = Timer(const Duration(milliseconds: 300), () {
      _searchStations(_searchController.text);
    });

    // REMOVED: Auto-expansion during search to prevent unwanted bubble effects
    // Users can manually expand the sheet if needed
  }

  Future<void> _searchStations(String query) async {
    debugPrint('\n=== SEARCHING STATIONS ===');
    debugPrint('Search query: "$query"');

    try {
      // Create an instance of ApiService
      final apiService = ApiService();

      // Call the search API
      final response = await apiService.searchStationsByName(query);

      if (response.success && response.data != null) {
        // Convert the search results to the format expected by the UI
        final searchResults = response.data!.map((station) {
          // Determine status based on the station status
          String status = station.status ?? 'Available';

          // Get connector types as a comma-separated string
          final connectorTypeStr = station.getConnectorTypesString();

          // Debug search station UID extraction
          debugPrint('🔍 MAPPING SEARCH STATION API DATA:');
          debugPrint('   📊 Station Name: ${station.name}');
          debugPrint('   🆔 Station ID: ${station.stationId}');
          debugPrint('   🔑 Station UID: ${station.uid}');
          debugPrint(
              '   📍 Coordinates: ${station.latitude}, ${station.longitude}');
          debugPrint('   🔍 UID Type: ${station.uid.runtimeType}');
          debugPrint('   ✅ UID Valid: ${station.uid?.isNotEmpty ?? false}');

          return {
            'id': station.stationId.toString(),
            'name': station.name ?? 'Unknown Station',
            'latitude': station.latitude ?? 0.0,
            'longitude': station.longitude ?? 0.0,
            'address': station.address ?? '',
            'city': station.city ?? '',
            'uid': station.uid ?? '', // Include UID from search station API
            'distance': 0.0, // Search results don't include distance
            'status': status,
            'availability': status,
            'connectorType': connectorTypeStr,
            'freeGuns': status == 'Available' ? 1 : 0,
            'mapPinUrl': status == 'Available'
                ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
                : (status == 'In Use'
                    ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                    : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
            'focusedMapPinUrl':
                'https://api2.eeil.online/mapicons/ecoplug_focus.png',
          };
        }).toList();

        debugPrint('Found ${searchResults.length} stations matching "$query"');

        setState(() {
          _filteredStations = searchResults;
          _errorMessage = null;
          _isSearchLoading = false;

          // If we have search results, update the map to show them
          if (searchResults.isNotEmpty && _googleMapKey.currentState != null) {
            // Focus the map on the first search result
            final firstStation = searchResults.first;
            final double lat = firstStation['latitude'] as double? ?? 0.0;
            final double lng = firstStation['longitude'] as double? ?? 0.0;

            if (lat != 0.0 && lng != 0.0) {
              _googleMapKey.currentState!.focusOnLocation(lat, lng, 12.0);
            }
          }
        });
      } else {
        debugPrint('Search API returned no results or error');
        setState(() {
          _filteredStations = [];
          _errorMessage = null;
          _isSearchLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error searching stations: $e');
      setState(() {
        _filteredStations = [];
        _errorMessage = null;
        _isSearchLoading = false;
      });
    }
  }

  // Method to load only map markers without updating the sheet
  Future<void> _loadMapMarkers({bool forceRefresh = false}) async {
    // Check if we already have markers in state and not forcing refresh
    final dashboardState = ref.read(dashboardNotifierProvider);
    if (!forceRefresh &&
        dashboardState.dataLoaded &&
        dashboardState.allMapMarkers.isNotEmpty) {
      debugPrint('\n=== USING MAP MARKERS FROM STATE ===');

      if (mounted) {
        setState(() {
          _allMapStations = dashboardState.allMapMarkers;
          _errorMessage = null;
        });
      }

      // Initialize PersistentMarkerService to preload images
      await _persistentMarkerService.initialize();

      // Preload important marker images for reliability
      await _persistentMarkerService.preloadCommonMarkers();

      return;
    }

    // If forceRefresh is true, invalidate the dashboard provider
    if (forceRefresh) {
      debugPrint('\n=== FORCE REFRESHING MAP MARKERS ===');
      ref.invalidate(dashboardNotifierProvider);
    }

    debugPrint('\n=== LOADING MAP MARKERS ===');

    try {
      // Create an instance of ApiBridge for better error handling
      final apiBridge = ApiBridge();

      debugPrint('Fetching station markers...');

      // Fetch station markers from the API
      final apiStations = await apiBridge.getApiStationMarkers();

      // Convert markers to the format expected by the UI
      final formattedStations = apiStations.map((marker) {
        final stationMap = apiBridge.convertMarkerToMapFormat(marker);

        // Ensure URLs are properly formatted with https://
        if (stationMap['mapPinUrl'] != null &&
            stationMap['mapPinUrl'].isNotEmpty) {
          String mapPinUrl = stationMap['mapPinUrl'];
          if (!mapPinUrl.startsWith('http')) {
            stationMap['mapPinUrl'] = 'https://$mapPinUrl';
          }
        }

        if (stationMap['focusedMapPinUrl'] != null &&
            stationMap['focusedMapPinUrl'].isNotEmpty) {
          String focusedMapPinUrl = stationMap['focusedMapPinUrl'];
          if (!focusedMapPinUrl.startsWith('http')) {
            stationMap['focusedMapPinUrl'] = 'https://$focusedMapPinUrl';
          }
        }

        return stationMap;
      }).toList();

      // Use the stations from the API
      debugPrint('Loaded ${formattedStations.length} markers from API');

      // Preload marker images with sequential processing for reliability
      if (mounted) {
        // Process markers in smaller batches to avoid overwhelming the system
        const int batchSize = 10;
        int processedCount = 0;

        for (int i = 0; i < formattedStations.length; i += batchSize) {
          final int end = (i + batchSize < formattedStations.length)
              ? i + batchSize
              : formattedStations.length;

          final batch = formattedStations.sublist(i, end);
          int batchSuccessCount = 0;

          // Process each station in batch
          for (var station in batch) {
            try {
              // Process regular marker URLs
              if (station['mapPinUrl'] != null &&
                  station['mapPinUrl'].isNotEmpty) {
                // Normalize URL
                String url = station['mapPinUrl'];
                if (!url.startsWith('http')) {
                  url = 'https://$url';
                  // Update the URL in the station data
                  station['mapPinUrl'] = url;
                }

                // Preload using persistent marker service instead of precacheImage
                await _persistentMarkerService.getBitmapDescriptorFromUrl(url);
                batchSuccessCount++;
              }

              // Process focused marker URLs
              if (station['focusedMapPinUrl'] != null &&
                  station['focusedMapPinUrl'].isNotEmpty) {
                // Normalize URL
                String url = station['focusedMapPinUrl'];
                if (!url.startsWith('http')) {
                  url = 'https://$url';
                  // Update the URL in the station data
                  station['focusedMapPinUrl'] = url;
                }

                // Preload using persistent marker service
                await _persistentMarkerService.getBitmapDescriptorFromUrl(url);
                batchSuccessCount++;
              }
            } catch (e) {
              debugPrint(
                  'Error preloading marker image for station ${station['name']}: $e');
            }
          }

          // Update progress
          processedCount += batch.length;
          debugPrint(
              'Preloaded marker images for batch: $batchSuccessCount successful '
              '($processedCount/${formattedStations.length} stations processed)');

          // Small delay between batches
          await Future.delayed(const Duration(milliseconds: 100));

          // Break if widget is unmounted
          if (!mounted) break;
        }
      }

      // Store markers in state
      ref
          .read(dashboardNotifierProvider.notifier)
          .updateMapMarkers(formattedStations);

      if (mounted) {
        setState(() {
          _allMapStations = formattedStations; // Store ALL stations for the map
          // Don't update _displayStations to keep the sheet empty
          _errorMessage = null; // Clear any previous error message
        });
      }
    } catch (e) {
      debugPrint('Error loading map markers: $e');

      // Just log the error, don't use fallback data
      if (mounted) {
        setState(() {
          _errorMessage = null; // Don't show error to user
        });
      }
    }
  }

  Future<void> _loadStationData() async {
    if (mounted) {
      setState(() {
        _isLoadingStations = true;
        _errorMessage = null;
        _isRefreshing = true;
      });
    }

    debugPrint('\n=== LOADING STATION DATA USING NEAREST STATION API ===');

    try {
      // Use the nearest station API instead of marker API
      // This ensures we get connector types and other detailed information

      // Use the current location or default to center of India
      // Only proceed if we have user location - don't use fallback coordinates
      if (_currentLatitude == null || _currentLongitude == null) {
        debugPrint(
            'Cannot load nearest stations - user location not available');
        return;
      }

      final LatLng locationToFetch =
          LatLng(_currentLatitude!, _currentLongitude!);

      debugPrint(
          'Fetching stations near: ${locationToFetch.latitude}, ${locationToFetch.longitude}');

      // Call the repository to get nearest stations with a large radius
      final apiResponse = await _stationRepository.getNearestStations(
        locationToFetch.latitude,
        locationToFetch.longitude,
        radius: 100.0, // Use a larger radius to get more stations
      );

      if (apiResponse.success == true && apiResponse.data != null) {
        debugPrint('API returned ${apiResponse.data!.length} stations');

        // Convert the API response to the format expected by the UI
        final formattedStations = apiResponse.data!.map((station) {
          // Determine status based on the station status
          String status = station.status ?? 'Available';

          // Get connector types as a comma-separated string
          final connectorTypeStr = station.getConnectorTypesString();

          // Get connector types with icons for UI display
          final connectorTypes = station.getConnectorTypes();

          // Calculate distance from the reference location
          double distance = station.distance ?? 0.0;

          // Direct UID extraction from NearestStation object
          final String extractedUid = station.uid;
          debugPrint('Extracted UID: $extractedUid from ${station.name}');

          return {
            'id': station.stationId.toString(),
            'name': station.name, // Real name from API - required field
            'latitude':
                station.latitude, // Real coordinates from API - required field
            'longitude':
                station.longitude, // Real coordinates from API - required field
            'address':
                station.address, // Real address from API - null if not provided
            'city': station.city, // Real city from API - null if not provided
            'uid': extractedUid, // Real UID from API
            'distance': distance,
            'status': status,
            'availability': status,
            'connectorType': connectorTypeStr,
            'connectorTypes':
                connectorTypes, // CRITICAL: Pass real connector data with icons
            'freeGuns': status == 'Available' ? 1 : 0,
            'mapPinUrl': status == 'Available'
                ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
                : (status == 'In Use'
                    ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                    : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
            'focusedMapPinUrl':
                'https://api2.eeil.online/mapicons/ecoplug_focus.png',
          };
        }).toList();

        // Use the stations from the API
        debugPrint(
            'Loaded ${formattedStations.length} stations from nearest station API');

        if (mounted) {
          setState(() {
            _allMapStations =
                formattedStations; // Store ALL stations for the map
            if (!_showingNearestStations) {
              // Only update display stations if not showing nearest
              _displayStations = formattedStations;
            }
            _isLoadingStations = false;
            _isRefreshing = false; // Update the refresh state
            _errorMessage = null; // Clear any previous error message
          });
        }
      } else {
        debugPrint('API returned no stations or error: ${apiResponse.message}');

        // Just log the error, don't show it to the user
        if (mounted) {
          setState(() {
            _isLoadingStations = false;
            _isRefreshing = false; // Update the refresh state
            _errorMessage = null; // Don't show error to user
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading station data: $e');

      // Just log the error, don't show it to the user
      if (mounted) {
        setState(() {
          _isLoadingStations = false;
          _isRefreshing = false; // Update the refresh state
          _errorMessage = null; // Don't show error to user
        });
      }
    }
  }

  // Load nearest stations based on user activity location
  Future<void> _loadNearestStations({bool forceRefresh = false}) async {
    debugPrint(
        '=== _loadNearestStations called (forceRefresh: $forceRefresh) ===');
    try {
      setState(() {
        _isLoadingNearestStations = true;
        _showingNearestStations = true;
      });

      // Use the user activity location, default to current location if null,
      // then fall back to initial location if both are null
      // Only proceed if we have user location - don't use fallback coordinates
      if (_userActivityLocation == null &&
          (_currentLatitude == null || _currentLongitude == null)) {
        debugPrint('Cannot load nearest stations - no location available');
        return;
      }

      final LatLng locationToFetch = _userActivityLocation ??
          LatLng(_currentLatitude!, _currentLongitude!);

      debugPrint('User activity location: $_userActivityLocation');
      debugPrint('Current location: $_currentLatitude, $_currentLongitude');
      debugPrint(
          'Fetching nearest stations at: ${locationToFetch.latitude}, ${locationToFetch.longitude}');

      // If forceRefresh is true, invalidate the dashboard provider
      if (forceRefresh) {
        debugPrint('Force refreshing nearest stations data');
        ref.invalidate(dashboardNotifierProvider);
      }

      debugPrint('Calling API to get nearest stations...');
      final apiResponse = await _stationRepository.getNearestStations(
        locationToFetch.latitude,
        locationToFetch.longitude,
        radius: _searchRadius,
      );

      debugPrint('Attempting to retrieve nearest stations, trial');
      for (int attempt = 1; attempt <= _maxRetryAttempts; attempt++) {
        try {
          final apiResponse = await _stationRepository.getNearestStations(
            locationToFetch.latitude,
            locationToFetch.longitude,
            radius: _searchRadius,
          );
          if (apiResponse.success == true && apiResponse.data != null) {
            debugPrint(
                'API returned ${apiResponse.data!.length} stations on attempt $attempt');
            break;
          }
        } catch (e) {
          debugPrint('Error on attempt $attempt: $e');
          if (attempt == _maxRetryAttempts) {
            throw e; // Re-throw the error if max attempts are reached
          }
        }
      }
      debugPrint(
          'API response received: success=${apiResponse.success}, data=${apiResponse.data != null ? 'not null' : 'null'}');

      if (apiResponse.success == true && apiResponse.data != null) {
        debugPrint('API returned ${apiResponse.data!.length} stations');

        // Convert the API response to the format expected by the UI
        final nearestStations = apiResponse.data!.map((station) {
          // Determine status based on the station status
          String status = station.status ?? 'Status unknown';

          // Get connector types as a comma-separated string
          final connectorTypeStr = station.getConnectorTypesString();

          // Get connector types with icons for UI display
          final connectorTypes = station.getConnectorTypes();

          // Calculate distance from the reference location
          double distance = station.distance ?? 0.0;

          // Create station data with normalized URLs
          debugPrint('🔄 MAPPING NEAREST STATION API DATA:');
          debugPrint('   📊 Station Name: ${station.name}');
          debugPrint('   🆔 Station ID: ${station.stationId}');
          debugPrint('   🔑 Station UID: ${station.uid}');
          debugPrint(
              '   📍 Coordinates: ${station.latitude}, ${station.longitude}');
          debugPrint('   🔍 UID Type: ${station.uid.runtimeType}');
          debugPrint('   ✅ UID Valid: ${station.uid.isNotEmpty}');

          final stationData = {
            'id': station.stationId.toString(),
            'name': station.name, // Real name from API - required field
            'latitude':
                station.latitude, // Real coordinates from API - required field
            'longitude':
                station.longitude, // Real coordinates from API - required field
            'address':
                station.address, // Real address from API - null if not provided
            'uid': station.uid, // Real UID from nearest station API
            'city': station.city, // Real city from API - null if not provided
            'distance': distance,
            'status': status,
            'availability': status,
            'connectorType': connectorTypeStr,
            'connectorTypes':
                connectorTypes, // CRITICAL: Pass real connector data with icons
            'freeGuns': status == 'Available' ? 1 : 0,
            'mapPinUrl': status == 'Available'
                ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
                : (status == 'In Use'
                    ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                    : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
            'focusedMapPinUrl':
                'https://api2.eeil.online/mapicons/ecoplug_focus.png',
          };

          // Preload marker images using PersistentMarkerService
          final String mapPinUrl = stationData['mapPinUrl'] as String;
          final String focusedMapPinUrl =
              stationData['focusedMapPinUrl'] as String;

          _persistentMarkerService.getBitmapDescriptorFromUrl(mapPinUrl);
          _persistentMarkerService.getBitmapDescriptorFromUrl(focusedMapPinUrl);

          return stationData;
        }).toList();

        debugPrint('Processed ${nearestStations.length} nearest stations');

        // REMOVED: Auto-expansion to prevent bubble-up effects during user interactions
        // Only the initial dashboard load should auto-expand the sheet

        // Store nearest stations in state
        ref
            .read(dashboardNotifierProvider.notifier)
            .updateNearestStations(nearestStations);

        setState(() {
          _nearestStations =
              nearestStations; // Store nearest stations separately
          _displayStations =
              nearestStations; // Update display stations for the sheet
          _filteredStations = [];
          _errorMessage = null;
          _isSearching = false;
          _isLoadingNearestStations = false; // Reset loading state
        });
        debugPrint(
            '📋 Updated display stations with ${_displayStations.length} nearest stations');
        debugPrint(
            '📋 Station names: ${_displayStations.map((s) => s['name']).take(3).join(', ')}${_displayStations.length > 3 ? '...' : ''}');

        // FIXED: Prevent bubble effect - do not auto-expand during data updates
        // Only allow auto-expand on initial dashboard load

        // Don't focus map here - let the calling method handle map transitions
        // This prevents conflicts with the smooth transition in _safelyUpdateLocation
      } else {
        debugPrint(
            'API returned no nearest stations or error: ${apiResponse.message}');
        setState(() {
          _nearestStations = [];
          _displayStations = _nearestStations; // Empty nearest stations
          _filteredStations = [];
          _errorMessage = 'No stations found nearby. Try a different location.';
          _isLoadingNearestStations = false; // Reset loading state
        });
        // Still trigger sheet expansion even if no stations found
        // This shows the user that the search was performed but no results were found
        debugPrint(
            '📋 No nearest stations found, but still showing sheet with message');
      }
    } catch (e) {
      debugPrint('Error loading nearest stations: $e');

      // Enhanced error handling with timeout-specific messages
      String errorMessage = 'Error loading stations. Please try again.';
      if (e.toString().toLowerCase().contains('timeout') ||
          e.toString().toLowerCase().contains('timed out')) {
        errorMessage =
            'Station loading is taking longer than expected. Please check your connection and try again.';
        debugPrint('🕐 Timeout error detected in station loading');
      } else if (e.toString().toLowerCase().contains('connection')) {
        errorMessage =
            'Unable to connect to the server. Please check your internet connection.';
        debugPrint('🌐 Connection error detected in station loading');
      }

      setState(() {
        _nearestStations = [];
        _displayStations = _nearestStations; // Empty nearest stations
        _filteredStations = [];
        _errorMessage = errorMessage;
        _isLoadingNearestStations = false; // Reset loading state
      });
      // Show user-friendly error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            action: SnackBarAction(
              label: 'RETRY',
              textColor: Colors.white,
              onPressed: () {
                debugPrint('🔄 Retrying station loading from error snackbar');
                _loadNearestStations();
              },
            ),
          ),
        );
      }
    } finally {
      setState(() {
        _isLoadingNearestStations = false;
      });
    }
  }

  // Method to handle map tap (currently just minimizes the sheet)

  // This method will be used when implementing the search functionality

  @override
  Widget build(BuildContext context) {
    // Define a variable to check if keyboard is visible
    final bool isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;

    return Scaffold(
      body: Stack(
        children: [
          // Google Map Widget with key for accessing its state
          // DASHBOARD CONFIGURATION: Clean map for station discovery
          GoogleMapWidget(
            key: _googleMapKey,
            stations: _allMapStations, // Always use full dataset for map
            onTap: _handleMapTapLocation, // Use the location tap handler
            onStationSelected:
                _onMarkerTapped, // Connect marker taps to _onMarkerTapped
            onLocationUpdated: _onLocationUpdated,
            onCameraPositionChanged:
                _onCameraPositionChanged, // Track camera changes
            initialLatitude: _indiaLatitude, // Always start with India view
            initialLongitude: _indiaLongitude, // Always start with India view
            initialZoom: _indiaZoom, // Show entire India initially
            // DASHBOARD: NO polylines, NO route features - clean station browsing
            polylines: const <Polyline>{}, // Explicitly NO polylines for dashboard
            fitBounds: null, // NO route bounds fitting
            onPolylineTapped: null, // NO polyline tap handlers
            additionalMarkers: null, // NO additional route markers
          ),

          // Active Sessions Battery Icon - Top Left Position
          if (_hasActiveSessions)
            Positioned(
              left: 16,
              top: MediaQuery.of(context).padding.top + 16,
              child: SlideTransition(
                position: _mapControlsAnimation,
                child: _buildNeonBatteryIcon(),
              ),
            ),

          // Map controls positioned at the right side
          Positioned(
            right: 16,
            top: MediaQuery.of(context).padding.top + 16,
            child: SlideTransition(
              position: _mapControlsAnimation,
              child: Column(
                children: [
                  _buildCircleIconButton(
                    icon: Icons.my_location,
                    tooltip: 'My Location',
                    onTap: () {
                      // Show a snackbar before starting location update
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Getting your location...'),
                          duration: Duration(seconds: 1),
                        ),
                      );

                      // Use the dedicated method to update location and show map
                      _updateLocationAndShowMap();
                    },
                  ),
                  const SizedBox(height: 12),
                  // Add animated refresh button below the target button
                  _buildAnimatedRefreshButton(),
                  const SizedBox(height: 12),
                  _buildCircleIconButton(
                    icon: Icons.list,
                    tooltip: 'Station List',
                    onTap: () {
                      // Use MaterialPageRoute instead of named route to ensure dark mode is applied
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const StationListPage(),
                        ),
                      ).then((_) => setState(() {}));
                    },
                  ),
                ],
              ),
            ),
          ),

          // Station list sheet with improved draggability
          FadeTransition(
            opacity: _stationListAnimation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 0.2),
                end: Offset.zero,
              ).animate(_stationListAnimation),
              child: NotificationListener<DraggableScrollableNotification>(
                onNotification: (notification) {
                  // If keyboard is visible and sheet is being dragged down, dismiss keyboard
                  if (isKeyboardVisible &&
                      notification.extent < _maxSheetSize - 0.05) {
                    FocusScope.of(context).unfocus();
                  }
                  return false;
                },
                child: Builder(builder: (context) {
                  // Create a local copy of stations to ensure it's not modified elsewhere
                  final stationsList =
                      List<Map<String, dynamic>>.from(_displayStations);

                  return StationListSheet(
                    sheetController: _sheetController,
                    searchController: _searchController,
                    stations: stationsList,
                    filteredStations: _filteredStations,
                    isLoading: _isLoadingStations || _isLoadingNearestStations,
                    isSearching: _isSearching,
                    isSearchLoading: _isSearchLoading,
                    showFilterOptions: _showFilterOptions,
                    buildStationCard: _buildStationCard,
                    onSearchChanged: _onSearchChanged,
                    onMarkerTapped: _onMarkerTapped,
                    onBackPressed: () {
                      // Additional actions when back button is pressed
                      FocusScope.of(context).unfocus();
                      // Minimize sheet when back is pressed
                      if (_sheetController.isAttached) {
                        _sheetController.animateTo(
                          _minSheetSize,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    },
                    minSheetSize: _minSheetSize,
                    maxSheetSize: _maxSheetSize,
                    showingNearestStations: _showingNearestStations,
                    // Error handling parameters
                    errorMessage: _errorMessage,
                    hasApiError:
                        _errorMessage != null && _errorMessage!.isNotEmpty,
                    onRetry: () {
                      debugPrint('🔄 Retrying station loading from sheet');
                      _loadNearestStations();
                    },
                    onExpandSearchRadius: () {
                      debugPrint('🔍 Expanding search radius');
                      // You can implement search radius expansion here
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content:
                              Text('Try moving the map to a different area'),
                          backgroundColor: Colors.blue,
                        ),
                      );
                    },
                    // Removed onRefresh - refresh functionality only available through map controls
                  );
                }),
              ),
            ),
          ),

          // Connector sheet if shown
          if (_showConnectorSheet) _buildConnectorSheet(),
        ],
      ),
    );
  }

  // Search handler for the search bar

  // Handle marker taps to show station details and update nearest stations
  Future<void> _onMarkerTapped(Map<String, dynamic> stationMap) async {
    debugPrint('=== DashboardScreen._onMarkerTapped called ===');
    debugPrint('Marker tapped for station: ${stationMap['name']}');

    // First check if the station already has a UID
    final String? uid = stationMap['uid']?.toString();
    debugPrint('Station UID from marker data: ${uid ?? "not available"}');

    // Store the UID in the station map if it exists
    if (uid != null && uid.isNotEmpty) {
      stationMap['uid'] = uid;
    }

    // Get station coordinates
    final double lat = stationMap['latitude'] as double? ?? 0.0;
    final double lng = stationMap['longitude'] as double? ?? 0.0;

    debugPrint('Station coordinates: $lat, $lng');

    if (lat != 0.0 && lng != 0.0) {
      debugPrint(
          'Valid coordinates - updating user activity location and loading nearest stations');

      // Update the user activity location with the station's coordinates
      setState(() {
        _userActivityLocation = LatLng(lat, lng);
        // Store a copy of the station map to avoid modifying the original
        selectedStation = Map<String, dynamic>.from(stationMap);
      });

      // Set loading state in the state
      setState(() {
        _isLoadingNearestStations = true;
      });

      try {
        // If the station doesn't have a UID, load nearest stations to find it
        if (uid == null || uid.isEmpty) {
          debugPrint('Station has no UID, loading nearest stations to find it');
          await _loadNearestStations();

          // Update loading state
          if (mounted) {
            setState(() {
              _isLoadingNearestStations = false;
            });
          }

          // Marker data does not contain UIDs - only use for map display
          // For station details navigation, use nearest stations API data instead
          debugPrint(
              'Marker tapped - markers do not contain UIDs for navigation');
        } else {
          // Station already has a UID, just update loading state
          if (mounted) {
            setState(() {
              _isLoadingNearestStations = false;
            });
          }
        }

        // Ensure marker images are cached using PersistentMarkerService
        if (selectedStation != null) {
          // Cache the regular marker image
          if (selectedStation!['mapPinUrl'] != null &&
              selectedStation!['mapPinUrl'].toString().isNotEmpty) {
            String url = selectedStation!['mapPinUrl'];
            if (!url.startsWith('http')) {
              url = 'https://$url';
            }
            _persistentMarkerService.getBitmapDescriptorFromUrl(url);
          }

          // Cache the focused marker image
          if (selectedStation!['focusedMapPinUrl'] != null &&
              selectedStation!['focusedMapPinUrl'].toString().isNotEmpty) {
            String url = selectedStation!['focusedMapPinUrl'];
            if (!url.startsWith('http')) {
              url = 'https://$url';
            }
            _persistentMarkerService.getBitmapDescriptorFromUrl(url);
          }
        }
      } catch (e) {
        debugPrint('Error loading nearest stations: $e');
        // Update loading state and show error
        if (mounted) {
          setState(() {
            _isLoadingNearestStations = false;
          });

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading station details: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } else {
      debugPrint('Invalid coordinates - only updating selected station');
      // Just set the selected station without updating location or loading nearest stations
      setState(() {
        selectedStation = Map<String, dynamic>.from(stationMap);
      });
    }

    // Auto-expand the sheet when map marker is tapped (user preference)
    debugPrint('Map marker tapped - auto-expanding station list sheet');
    if (_sheetController.isAttached) {
      _sheetController.animateTo(
        _midSheetSize, // Expand to mid-size to show station list
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeOutCubic,
      );
    }
  }

  void _closeConnectorSheet() {
    setState(() {
      _showConnectorSheet = false;
    });
  }

  Widget _buildConnectorSheet() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.1),
            blurRadius: 16,
            spreadRadius: 8,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Select Connector',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _closeConnectorSheet,
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: Text('Connector selection content goes here')),
          ),
        ],
      ),
    );
  }

  // Improved circle icon button with curved design
  Widget _buildCircleIconButton({
    required IconData icon,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = const Color(0xFF67C44C);

    return Tooltip(
      message: tooltip,
      child: Material(
        elevation: 4,
        shadowColor: isDarkMode ? Colors.black38 : Colors.black26,
        shape: const CircleBorder(),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: onTap,
          customBorder: const CircleBorder(),
          splashColor: primaryColor.withAlpha(50),
          highlightColor: primaryColor.withAlpha(30),
          child: Container(
            width: 52,
            height: 52,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDarkMode ? const Color(0xFF2C2C2C) : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withAlpha(51) // ~0.2 opacity
                      : Colors.black.withAlpha(25), // ~0.1 opacity
                  blurRadius: 4,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Icon(
              icon,
              color: primaryColor,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  // Animated refresh button with rotation animation
  Widget _buildAnimatedRefreshButton() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = const Color(0xFF67C44C);

    return Tooltip(
      message: 'Refresh Stations',
      child: Material(
        elevation: 4,
        shadowColor: isDarkMode ? Colors.black38 : Colors.black26,
        shape: const CircleBorder(),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: _isRefreshAnimating
              ? null
              : () {
                  _refreshStationMarkers();
                },
          customBorder: const CircleBorder(),
          splashColor: primaryColor.withAlpha(50),
          highlightColor: primaryColor.withAlpha(30),
          child: Container(
            width: 52,
            height: 52,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDarkMode ? const Color(0xFF2C2C2C) : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withAlpha(51) // ~0.2 opacity
                      : Colors.black.withAlpha(25), // ~0.1 opacity
                  blurRadius: 4,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: AnimatedBuilder(
              animation: _refreshAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _refreshAnimation.value *
                      2 *
                      3.14159, // 2π for full rotation
                  child: Icon(
                    Icons.refresh,
                    color: _isRefreshAnimating
                        ? primaryColor
                            .withAlpha(180) // Slightly dimmed during animation
                        : primaryColor,
                    size: 24,
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // Build modern neon dark green battery icon for top-left position
  Widget _buildNeonBatteryIcon() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Modern neon dark green color palette
    const neonGreen = Color(0xFF00FF41); // Bright neon green
    const darkGreen = Color(0xFF0D4F3C); // Dark green base
    const glowGreen = Color(0xFF00CC33); // Glow effect green

    return Tooltip(
      message: 'Active Charging Sessions',
      child: Material(
        elevation: 8,
        shadowColor: isDarkMode
            ? neonGreen.withValues(alpha: 0.3)
            : Colors.black.withValues(alpha: 0.2),
        shape: const CircleBorder(),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: _navigateToActiveSessions,
          customBorder: const CircleBorder(),
          splashColor: neonGreen.withValues(alpha: 0.2),
          highlightColor: glowGreen.withValues(alpha: 0.1),
          child: Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  darkGreen,
                  darkGreen.withValues(alpha: 0.8),
                  Colors.black.withValues(alpha: 0.9),
                ],
                stops: const [0.0, 0.7, 1.0],
              ),
              border: Border.all(
                color: neonGreen.withValues(alpha: 0.6),
                width: 1.5,
              ),
              boxShadow: [
                // Neon glow effect
                BoxShadow(
                  color: neonGreen.withValues(alpha: 0.4),
                  blurRadius: 12,
                  spreadRadius: 3,
                ),
                // Inner glow
                BoxShadow(
                  color: glowGreen.withValues(alpha: 0.3),
                  blurRadius: 6,
                  spreadRadius: 1,
                ),
                // Depth shadow
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.5)
                      : Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Modern battery icon with neon effect
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        neonGreen.withValues(alpha: 0.1),
                        Colors.transparent,
                      ],
                      stops: const [0.0, 1.0],
                    ),
                  ),
                  child: Icon(
                    Icons.battery_charging_full,
                    color: neonGreen,
                    size: 28,
                    shadows: [
                      Shadow(
                        color: neonGreen.withValues(alpha: 0.8),
                        blurRadius: 4,
                      ),
                      Shadow(
                        color: glowGreen.withValues(alpha: 0.6),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                ),
                // Pulsing neon animation overlay
                AnimatedBuilder(
                  animation: _batteryPulseAnimation,
                  builder: (context, child) {
                    final pulseValue = _batteryPulseAnimation.value;
                    return Container(
                      width: 56 + (pulseValue * 12),
                      height: 56 + (pulseValue * 12),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: neonGreen.withValues(
                            alpha: 0.4 * (1 - pulseValue),
                          ),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: neonGreen.withValues(
                              alpha: 0.3 * (1 - pulseValue),
                            ),
                            blurRadius: 8 + (pulseValue * 4),
                            spreadRadius: pulseValue * 2,
                          ),
                        ],
                      ),
                    );
                  },
                ),
                // Additional inner pulse for enhanced neon effect
                AnimatedBuilder(
                  animation: _batteryPulseAnimation,
                  builder: (context, child) {
                    final innerPulse = _batteryPulseAnimation.value * 0.5;
                    return Container(
                      width: 40 + (innerPulse * 8),
                      height: 40 + (innerPulse * 8),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: glowGreen.withValues(
                            alpha: 0.2 * (1 - innerPulse),
                          ),
                          width: 1,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Load nearest stations using default location (center of India)
  Future<void> _loadNearestStationsWithDefaultLocation() async {
    debugPrint(
        'Loading nearest stations with default location (center of India)');

    setState(() {
      _isLoadingNearestStations = true;
      _showingNearestStations = true;
    });

    try {
      // Use the center of India as the default location
      final LatLng defaultLocation =
          const LatLng(_indiaLatitude, _indiaLongitude);

      // Set user activity location to default location
      setState(() {
        _userActivityLocation = defaultLocation;
      });

      // Call the repository to get nearest stations
      final apiResponse = await _stationRepository.getNearestStations(
        defaultLocation.latitude,
        defaultLocation.longitude,
        radius: 100.0, // Use a larger radius to get more stations
      );

      if (apiResponse.success == true && apiResponse.data != null) {
        debugPrint(
            'API returned ${apiResponse.data!.length} stations for default location');

        // Convert the API response to the format expected by the UI
        final nearestStations = apiResponse.data!.map((station) {
          // Determine status based on the station status
          String status = station.status ?? 'Available';

          // Get connector types as a comma-separated string
          final connectorTypeStr = station.getConnectorTypesString();

          // Calculate distance from the reference location
          double distance = station.distance ?? 0.0;

          return {
            'id': station.stationId.toString(),
            'name': station.name ?? 'Unknown Station',
            'latitude': station.latitude ?? 0.0,
            'longitude': station.longitude ?? 0.0,
            'address': station.address ?? '',
            'city': station.city ?? '',
            'uid': station.uid, // Include the UID from the station
            'distance': distance,
            'status': status,
            'availability': status,
            'connectorType': connectorTypeStr,
            'freeGuns': status == 'Available' ? 1 : 0,
            'mapPinUrl': status == 'Available'
                ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
                : (status == 'In Use'
                    ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                    : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
            'focusedMapPinUrl':
                'https://api2.eeil.online/mapicons/ecoplug_focus.png',
          };
        }).toList();

        // Store nearest stations in state
        ref
            .read(dashboardNotifierProvider.notifier)
            .updateNearestStations(nearestStations);

        if (mounted) {
          setState(() {
            _nearestStations = nearestStations;
            _displayStations = nearestStations;
            _isLoadingNearestStations = false;
            _errorMessage = null;
          });

          // REMOVED: Auto-expansion to prevent bubble-up effects during fallback loading
          // Only the initial dashboard load should auto-expand the sheet
        }
      } else {
        debugPrint('API returned no stations or error for default location');
        if (mounted) {
          setState(() {
            _isLoadingNearestStations = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading nearest stations with default location: $e');
      if (mounted) {
        setState(() {
          _isLoadingNearestStations = false;
        });
      }
    }
  }

  // Separate method to handle location update and map focus
  Future<void> _updateLocationAndShowMap() async {
    try {
      // Reset auto-centering behavior when user explicitly requests location
      if (_googleMapKey.currentState != null) {
        _googleMapKey.currentState!.resetAutoCentering();
      }

      // Update location and load nearest stations
      await _safelyUpdateLocation(loadNearestStations: true);

      // Focus map on location if available
      if (mounted &&
          _googleMapKey.currentState != null &&
          _currentLatitude != null &&
          _currentLongitude != null) {
        _googleMapKey.currentState!.focusOnLocation(
          _currentLatitude!,
          _currentLongitude!,
          15.0,
        );
      }
    } catch (e) {
      debugPrint('Error updating location and showing map: $e');
    }
  }

  // Removed confirmation dialog as per user's request
  // Method to refresh station markers with improved error handling and data flow
  Future<void> _refreshStationMarkers() async {
    // Prevent multiple refresh operations
    if (_isRefreshAnimating) {
      debugPrint('🔄 Refresh already in progress, ignoring tap');
      return;
    }

    // Show a loading snackbar
    if (!mounted) return;

    // Start refresh animation
    _startRefreshAnimation();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Refreshing station data...'),
        duration: Duration(seconds: 1),
      ),
    );

    try {
      // Force refresh using state management
      await ref.read(dashboardNotifierProvider.notifier).forceRefresh();

      // Reload map markers with force refresh
      await _loadMapMarkers(forceRefresh: true);

      // If we have location, reload nearest stations
      if (_currentLatitude != null && _currentLongitude != null) {
        await _loadNearestStations(forceRefresh: true);
      }

      // Invalidate location cache to get fresh location on next request
      final locationService = LocationService();
      locationService.invalidateLocation();
      // Clear marker caches for a complete refresh
      await _persistentMarkerService.clearCache();

      // Refresh the marker API using forced refresh via StationService
      final stationService = StationService(
        ConnectivityService(),
        StationRepository(),
        ApiService(),
      );
      final response =
          await stationService.getStationMarkers(forceRefresh: true);

      // Check if widget is still mounted before updating UI
      if (!mounted) return;

      if (response.success) {
        // Re-initialize PersistentMarkerService and preload common markers
        await _persistentMarkerService.initialize();
        await _persistentMarkerService.preloadCommonMarkers();

        // After getting fresh markers, reload the map with forced refresh
        await _loadMapMarkers(forceRefresh: true);

        // If we have a location, also reload nearest stations
        if (_currentLatitude != null && _currentLongitude != null) {
          await _loadNearestStations(forceRefresh: true);
        } else {
          // If no location, load station data with a larger radius
          await _loadStationData();
        }

        // Update UI state
        setState(() {
          _isRefreshing = false;
        });

        // Check if still mounted before showing success message
        if (!mounted) return;

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Stations refreshed successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Invalidate the dashboard provider to ensure all components are updated
        ref.invalidate(dashboardNotifierProvider);
      } else {
        // Check if still mounted before showing error message
        if (!mounted) return;

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      // Handle any errors
      if (!mounted) return;
      debugPrint('Error refreshing markers: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error refreshing markers: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      // CRITICAL FIX: Always stop animation in finally block to prevent infinite rotation
      _stopRefreshAnimation();
      debugPrint('🔄 Refresh animation stopped in finally block');
    }
  }

  void _showFilterOptions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => FilterDialog(
        selectedConnectorFilters: Map.from(_selectedConnectorFilters),
        showOnlyAvailable: _showOnlyAvailable,
        onApplyFilters: (connectorFilters, onlyAvailable) {
          setState(() {
            _selectedConnectorFilters.clear();
            _selectedConnectorFilters.addAll(connectorFilters);
            _showOnlyAvailable = onlyAvailable;
          });

          // Apply the filters
          _applyFilters();
        },
      ),
    );
  }

  // Track selected filters
  final Map<String, bool> _selectedConnectorFilters = {
    'CCS2': false,
    'Type 2': false,
    'CHAdeMO': false,
    'GB/T': false,
  };

  bool _showOnlyAvailable = false;

  // Apply filters to stations
  void _applyFilters() {
    setState(() {
      // If no filters are selected, show all stations
      bool hasConnectorFilter =
          _selectedConnectorFilters.values.any((selected) => selected);

      if (!hasConnectorFilter && !_showOnlyAvailable) {
        _filteredStations = List.from(_displayStations);
        return;
      }

      _filteredStations = _displayStations.where((station) {
        // Filter by availability if needed
        if (_showOnlyAvailable && station['status'] != 'Available') {
          return false;
        }

        // If no connector filters are selected, only apply availability filter
        if (!hasConnectorFilter) {
          return true;
        }

        // Check if station has any of the selected connector types
        String connectorTypeStr = station['connectorType'] as String? ?? '';

        // Check each selected connector type
        for (var entry in _selectedConnectorFilters.entries) {
          if (entry.value && connectorTypeStr.contains(entry.key)) {
            return true;
          }
        }

        // If we have connector filters but none matched, exclude this station
        return false;
      }).toList();

      // Update the search flag to show filtered results
      _isSearching = hasConnectorFilter || _showOnlyAvailable;
    });
  }

  Widget _buildStationCard(Map<String, dynamic> station) {
    final stationName = station['name'] ?? 'Unknown Station';
    final address = station['address'] ?? 'No Address';

    // Get station coordinates
    final stationLat = station['latitude'] as double? ?? 0.0;
    final stationLng = station['longitude'] as double? ?? 0.0;

    // Calculate distance based on user's current location if available
    double distance = station['distance'] ?? 0.0;

    // Get connector types
    String connectorTypeStr = station['connectorType'] ?? 'Various';

    // If we have the user's current location, calculate the distance
    if (_hasLocationUpdate &&
        _currentLatitude != null &&
        _currentLongitude != null) {
      // Use the geolocator package to calculate distance
      try {
        // Import at the top of the file: import 'package:geolocator/geolocator.dart' as geo;
        distance = geo.Geolocator.distanceBetween(
              _currentLatitude!,
              _currentLongitude!,
              stationLat,
              stationLng,
            ) /
            1000; // Convert meters to kilometers
      } catch (e) {
        debugPrint('Error calculating distance: $e');
        // Fall back to the distance from the API
        distance = station['distance'] ?? 0.0;
      }
    }

    final availability =
        station['status'] ?? station['availability'] ?? 'Available';

    // Determine color based on availability status
    Color availabilityColor;
    if (availability == 'Available') {
      availabilityColor = const Color(0xFF34C759); // Green
    } else if (availability == 'In Use') {
      availabilityColor = Colors.orange; // Orange
    } else {
      availabilityColor = Colors.red; // Red for Unavailable
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: Card(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 3,
        child: InkWell(
          onTap: () async {
            // Extract station data for map navigation
            final String stationName = station['name'] ?? 'Unknown Station';
            final double stationLat = station['latitude'] as double? ?? 0.0;
            final double stationLng = station['longitude'] as double? ?? 0.0;

            debugPrint('🎯 STATION CARD TAPPED: $stationName');
            debugPrint('📍 Coordinates: $stationLat, $stationLng');

            // Focus map on station location
            if (stationLat != 0.0 && stationLng != 0.0) {
              debugPrint('✅ Focusing map on station location');

              // Focus the map on the station's location
              if (_googleMapKey.currentState != null) {
                _googleMapKey.currentState!.focusOnLocation(
                  stationLat,
                  stationLng,
                  16.0, // Higher zoom level for better station view
                );

                // Minimize the sheet to better show the map
                if (_sheetController.isAttached) {
                  _sheetController.animateTo(
                    _minSheetSize,
                    duration: const Duration(milliseconds: 400),
                    curve: Curves.easeOutCubic,
                  );
                }
              } else {
                debugPrint('❌ Map controller not available');
              }
            } else {
              debugPrint('❌ Invalid station coordinates');
            }
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            stationName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            address,
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: availabilityColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: availabilityColor.withAlpha(76),
                          width: 0.5,
                        ),
                      ),
                      child: Text(
                        availability,
                        style: TextStyle(
                          color: availabilityColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    // Left side with connector type and REAL API ICON
                    Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Color.fromRGBO(61, 122, 245, 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: _buildDashboardConnectorIcon(station),
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              connectorTypeStr,
                              style: const TextStyle(
                                color: Color(0xFF3D7AF5),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Right side with distance and free guns
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.location_on,
                          color: Colors.grey.shade600,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${distance.toStringAsFixed(1)} km',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      flex: 1, // Equal flex for both buttons
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          // BULLETPROOF UID EXTRACTION AND VALIDATION
                          debugPrint(
                              '🔒 BULLETPROOF START CHARGING BUTTON TAPPED');
                          debugPrint('🔒 Station Data: ${station.toString()}');

                          // Simple UID extraction from station data
                          final String extractedUid =
                              station['uid']?.toString() ?? '';
                          debugPrint('Start Charging - UID: $extractedUid');

                          if (extractedUid.isNotEmpty) {
                            debugPrint('UID found: $extractedUid');
                            if (mounted) {
                              try {
                                await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        StationDetailsPage(uid: extractedUid),
                                  ),
                                );
                              } catch (e) {
                                debugPrint('Navigation error: $e');
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                          'Failed to open station details: $e'),
                                      backgroundColor: Colors.red,
                                      duration: const Duration(seconds: 3),
                                    ),
                                  );
                                }
                              }
                            }
                          } else {
                            debugPrint(
                                'No UID available for station details navigation');
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: const Text(
                                      'Station details not available. Please use the Station List for charging options.'),
                                  duration: const Duration(seconds: 4),
                                  backgroundColor: Colors.orange,
                                  action: SnackBarAction(
                                    label: 'Station List',
                                    textColor: Colors.white,
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              const StationListPage(),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              );
                            }
                          }
                        },
                        icon: const Icon(Icons.power,
                            size: 18, color: Colors.white),
                        label: const Text(
                          'Start Charging',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF34C759),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          minimumSize: const Size.fromHeight(
                            45,
                          ), // Increased height
                          elevation: 2,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      flex: 1, // Equal flex for directions button
                      child: ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Colors.blue, // Fixed: Use standard blue color
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          minimumSize: const Size.fromHeight(45),
                          elevation: 2,
                        ),
                        onPressed: () async {
                          final lat =
                              station['latitude'] as double? ?? _indiaLatitude;
                          final lng = station['longitude'] as double? ??
                              _indiaLongitude;
                          final uri = Uri.parse(
                              'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng');
                          try {
                            if (await canLaunchUrl(uri)) {
                              await launchUrl(uri,
                                  mode: LaunchMode.externalApplication);
                            } else {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Could not open directions'),
                                  ),
                                );
                              }
                            }
                          } catch (e) {
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Error: $e'),
                                ),
                              );
                            }
                          }
                        },
                        icon: const Icon(Icons.directions, size: 18),
                        label: const Text(
                          'Directions',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Build connector icon from API data for dashboard cards
  Widget _buildDashboardConnectorIcon(Map<String, dynamic> station) {
    // Get connector types with icons from station data
    final List<dynamic>? connectorTypes = station['connectorTypes'];

    if (connectorTypes != null && connectorTypes.isNotEmpty) {
      // Use the first connector type with icon
      final firstConnector = connectorTypes.first;
      if (firstConnector is Map && firstConnector.containsKey('icon')) {
        final String? iconUrl = firstConnector['icon']?.toString();
        final String connectorType =
            firstConnector['name']?.toString() ?? 'Unknown';

        // If we have a real icon URL from API, use it
        if (iconUrl != null && iconUrl.isNotEmpty) {
          return SizedBox(
            width: 16,
            height: 16,
            child: Image.network(
              iconUrl,
              width: 16,
              height: 16,
              color: const Color(0xFF3D7AF5),
              errorBuilder: (context, error, stackTrace) {
                debugPrint('Error loading connector icon: $iconUrl');
                return const Icon(
                  Icons.bolt,
                  color: Color(0xFF3D7AF5),
                  size: 16,
                );
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF3D7AF5)),
                  ),
                );
              },
            ),
          );
        }

        // Fallback to appropriate icon based on connector type
        IconData iconData = Icons.bolt;
        if (connectorType.toLowerCase().contains('ccs')) {
          iconData = Icons.flash_on;
        } else if (connectorType.toLowerCase().contains('chademo')) {
          iconData = Icons.electrical_services;
        } else if (connectorType.toLowerCase().contains('type2')) {
          iconData = Icons.power;
        }

        return Icon(
          iconData,
          color: const Color(0xFF3D7AF5),
          size: 16,
        );
      }
    }

    // Default fallback icon
    return const Icon(
      Icons.bolt,
      color: Color(0xFF3D7AF5),
      size: 16,
    );
  }

  // Handle camera position changes to preserve map state
  void _onCameraPositionChanged(CameraPosition position) {
    // Update camera position in state management
    ref.read(dashboardNotifierProvider.notifier).updateCameraPosition(position);
  }

  // Handle sheet position changes to preserve sheet state
  void _onSheetPositionChanged() {
    if (_sheetController.isAttached) {
      final position = _sheetController.size;
      ref
          .read(dashboardNotifierProvider.notifier)
          .updateSheetPosition(position);
    }
  }

  // Station details navigation uses UIDs from:
  // - Nearest Station API (/user/stations/nearest)
  // - Station Search API (/user/station/search)
  // - Station Paginate API (/user/station/paginate)
  // If a station doesn't have a UID, we show an error message to the user
}
